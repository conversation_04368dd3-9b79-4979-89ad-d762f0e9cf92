import { useEffect, useRef, useState } from 'react';
import { supabase } from '../lib/supabase';
import { GameService } from '../services/GameService';
import { GameParticipant, GameSession, Question } from '../types';

export const useGame = (gameId?: string) => {
  const [game, setGame] = useState<GameSession | null>(null);
  const [participants, setParticipants] = useState<GameParticipant[]>([]);
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Load initial game data when gameId is set
  useEffect(() => {
    if (!gameId) return;

    const loadInitialData = async () => {
      try {
        const [gameData, participantsData] = await Promise.all([
          GameService.getGameSession(gameId),
          GameService.getGameParticipants(gameId),
        ]);

        console.log('Initial game data loaded - participants:', participantsData.length);
        setGame(gameData);
        setParticipants(participantsData);
      } catch (err) {
        console.error('Error loading initial game data:', err);
        setError((err as Error).message);
      }
    };

    loadInitialData();
  }, [gameId]);

  // Real-time subscriptions
  useEffect(() => {
    if (!gameId) return;

    const gameChannel = supabase
      .channel(`game_${gameId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'game_sessions',
        filter: `id=eq.${gameId}`,
      }, (payload) => {
        console.log('Game session updated:', payload.new);
        setGame(payload.new as GameSession);
      })
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'game_participants',
        filter: `game_id=eq.${gameId}`,
      }, async () => {
        // Refetch participants when changes occur
        console.log('Participants changed, refetching...');
        const updatedParticipants = await GameService.getGameParticipants(gameId);
        console.log('Updated participants:', updatedParticipants.length);
        setParticipants(updatedParticipants);
      })
      .subscribe();

    return () => {
      gameChannel.unsubscribe();
    };
  }, [gameId]);

  // Timer management
  useEffect(() => {
    if (timeRemaining > 0) {
      timerRef.current = setTimeout(() => {
        setTimeRemaining(prev => prev - 1);
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [timeRemaining]);

  const joinGame = async (targetGameId: string, userId: string) => {
    try {
      setLoading(true);
      setError(null);

      const participant = await GameService.joinGame(targetGameId, userId);
      const [gameData, participantsData] = await Promise.all([
        GameService.getGameSession(targetGameId),
        GameService.getGameParticipants(targetGameId),
      ]);

      console.log('Joined game - participants:', participantsData.length);
      setGame(gameData);
      setParticipants(participantsData);
    } catch (err) {
      setError((err as Error).message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const leaveGame = async (userId: string) => {
    if (!gameId) return;
    
    try {
      await GameService.leaveGame(gameId, userId);
      setGame(null);
      setParticipants([]);
    } catch (err) {
      setError((err as Error).message);
      throw err;
    }
  };

  const setReady = async (userId: string, isReady: boolean) => {
    if (!gameId) return;
    
    try {
      await GameService.setPlayerReady(gameId, userId, isReady);
    } catch (err) {
      setError((err as Error).message);
      throw err;
    }
  };

  const submitAnswer = async (
    userId: string, 
    questionId: string, 
    answer: string, 
    timeTaken: number
  ) => {
    if (!gameId) return;
    
    try {
      const result = await GameService.submitAnswer(gameId, userId, questionId, answer, timeTaken);
      return result;
    } catch (err) {
      setError((err as Error).message);
      throw err;
    }
  };

  const startTimer = (duration: number) => {
    setTimeRemaining(duration);
  };

  const refreshGameData = async () => {
    if (!gameId) return;

    try {
      setLoading(true);
      const [gameData, participantsData] = await Promise.all([
        GameService.getGameSession(gameId),
        GameService.getGameParticipants(gameId),
      ]);

      console.log('Manual refresh - participants:', participantsData.length, 'status:', gameData.status);
      setGame(gameData);
      setParticipants(participantsData);
    } catch (err) {
      setError((err as Error).message);
      console.error('Error refreshing game data:', err);
    } finally {
      setLoading(false);
    }
  };

  return {
    game,
    participants,
    currentQuestion,
    timeRemaining,
    loading,
    error,
    joinGame,
    leaveGame,
    setReady,
    submitAnswer,
    startTimer,
    setCurrentQuestion,
    refreshGameData,
  };
};