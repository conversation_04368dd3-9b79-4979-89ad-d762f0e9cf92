import InputField from '@/components/InputField'
import { useAuth } from '@/hooks/useAuth'
import { router } from 'expo-router'
import React, { useState } from 'react'
import { Alert, Text, TouchableOpacity, View } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'

const Signin = () => {
  const { signIn, loading, error } = useAuth()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')

  const handleSubmit = async () => {
    try {
      await signIn(email, password)
      router.push('/(root)/(tabs)/home')
    } catch (err) {
      const errorMessage = error?.includes('Email not confirmed') 
        ? 'Please check your email for a confirmation link. You need to confirm your email before you can sign in.'
        : error || 'Failed to sign in'
      Alert.alert('Error', errorMessage)
    }
  }

  return (
    <SafeAreaView className='flex-1 bg-[#e0e2e4] p-4 gap-4 justify-center'>
      <InputField 
        image={require('../../assets/images/Profile.png')}
        placeholder='Email'
        value={email}
        onChangeText={setEmail}
        keyboardType="email-address"
        autoCapitalize="none"
      />
      <InputField 
        image={require('../../assets/images/Lock.png')}
        placeholder='Password'
        value={password}
        onChangeText={setPassword}
        secureTextEntry
      />
      <TouchableOpacity 
        className='bg-[#1d1d1d] rounded-full' 
        onPress={handleSubmit}
        disabled={loading}
      >
        <Text className='text-white p-6 font-HelveticaNeueMedium text-center'>
          {loading ? 'Signing in...' : 'Sign In'}
        </Text>
      </TouchableOpacity>
      <View className='flex-row justify-center items-center gap-2'>
        <Text className='text-[#1d1d1d] font-HelveticaNeueMedium'>Don't have an account?</Text>
        <TouchableOpacity onPress={() => router.push('/(auth)/signup')}>
          <Text className='text-[#1d1d1d] font-HelveticaNeueMedium underline'>Sign Up</Text>
        </TouchableOpacity>
      </View>
      <View className='flex-row justify-center items-center gap-2'>
        <TouchableOpacity onPress={() => router.push('/(auth)/forgot-password')}>
          <Text className='text-[#1d1d1d] font-HelveticaNeueMedium underline'>Forgot your password?</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  )
}

export default Signin