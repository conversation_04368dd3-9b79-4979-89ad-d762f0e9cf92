import { Ionicons } from '@expo/vector-icons';
import * as FileSystem from 'expo-file-system';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

interface DownloadedFile {
  name: string;
  path: string;
  category: string;
  section: string;
  size: number;
  dateDownloaded: string;
  type: 'pdf' | 'image' | 'document' | 'other';
}

interface DownloadsSummaryProps {
  refreshTrigger?: number;
}

const DownloadsSummary = ({ refreshTrigger }: DownloadsSummaryProps) => {
  const [recentDownloads, setRecentDownloads] = useState<DownloadedFile[]>([]);
  const [totalFiles, setTotalFiles] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadRecentDownloads();
  }, [refreshTrigger]);

  const loadRecentDownloads = async () => {
    try {
      setLoading(true);

      const appName = 'quizzy';
      const downloadsPath = `${FileSystem.documentDirectory}${appName}/courses`;
      
      const dirInfo = await FileSystem.getInfoAsync(downloadsPath);
      if (!dirInfo.exists) {
        setRecentDownloads([]);
        setTotalFiles(0);
        return;
      }

      const files: DownloadedFile[] = [];
      
      // Get all categories
      const categories = await FileSystem.readDirectoryAsync(downloadsPath);
      
      for (const category of categories) {
        const categoryPath = `${downloadsPath}/${category}`;
        const categoryInfo = await FileSystem.getInfoAsync(categoryPath);
        
        if (categoryInfo.isDirectory) {
          // Get all sections in this category
          const sections = await FileSystem.readDirectoryAsync(categoryPath);
          
          for (const section of sections) {
            const sectionPath = `${categoryPath}/${section}`;
            const sectionInfo = await FileSystem.getInfoAsync(sectionPath);
            
            if (sectionInfo.isDirectory) {
              // Get all files in this section
              const sectionFiles = await FileSystem.readDirectoryAsync(sectionPath);
              
              for (const fileName of sectionFiles) {
                const filePath = `${sectionPath}/${fileName}`;
                const fileInfo = await FileSystem.getInfoAsync(filePath);
                
                if (!fileInfo.isDirectory) {
                  files.push({
                    name: fileName,
                    path: filePath,
                    category: category.replace(/_/g, ' ').toUpperCase(),
                    section: section.charAt(0).toUpperCase() + section.slice(1),
                    size: (fileInfo as any).size || 0,
                    dateDownloaded: new Date((fileInfo as any).modificationTime || Date.now()).toLocaleDateString(),
                    type: getFileType(fileName),
                  });
                }
              }
            }
          }
        }
      }

      // Sort by date downloaded (newest first) and take only the first 3
      files.sort((a, b) => new Date(b.dateDownloaded).getTime() - new Date(a.dateDownloaded).getTime());
      
      setRecentDownloads(files.slice(0, 3));
      setTotalFiles(files.length);
    } catch (err) {
      console.error('Error loading recent downloads:', err);
      setRecentDownloads([]);
      setTotalFiles(0);
    } finally {
      setLoading(false);
    }
  };

  const getFileType = (fileName: string): 'pdf' | 'image' | 'document' | 'other' => {
    const extension = fileName.toLowerCase().split('.').pop();
    
    if (extension === 'pdf') return 'pdf';
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(extension || '')) return 'image';
    if (['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'].includes(extension || '')) return 'document';
    return 'other';
  };

  const getFileIcon = (type: string, fileName?: string) => {
    switch (type) {
      case 'pdf':
        return 'document-text';
      case 'image':
        return 'image';
      case 'document':
        const ext = fileName?.toLowerCase().split('.').pop();
        if (['ppt', 'pptx'].includes(ext || '')) return 'easel';
        if (['xls', 'xlsx'].includes(ext || '')) return 'grid';
        return 'document-text';
      default:
        return 'document';
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <View className="px-6 py-4">
        <Text className="text-xl font-bold text-gray-900 mb-4">📁 Downloads</Text>
        <View className="bg-white rounded-2xl p-4 shadow-sm">
          <Text className="text-gray-500 text-center">Loading downloads...</Text>
        </View>
      </View>
    );
  }

  return (
    <View className="px-6 py-4">
      <View className="flex-row justify-between items-center mb-4">
        <Text className="text-xl font-bold text-gray-900">📁 Downloads</Text>
        <TouchableOpacity
          onPress={() => router.push('/downloads')}
          className="flex-row items-center"
        >
          <Text className="text-blue-600 font-medium mr-1">View All</Text>
          <Ionicons name="chevron-forward" size={16} color="#2563EB" />
        </TouchableOpacity>
      </View>

      <View className="bg-white rounded-2xl p-4 shadow-sm">
        {totalFiles === 0 ? (
          <TouchableOpacity
            onPress={() => router.push('/downloads')}
            className="items-center py-4"
          >
            <Ionicons name="download-outline" size={32} color="#9CA3AF" />
            <Text className="text-gray-500 text-center mt-2">No downloads yet</Text>
            <Text className="text-gray-400 text-center text-sm">Downloaded files will appear here</Text>
          </TouchableOpacity>
        ) : (
          <>
            <View className="flex-row items-center justify-between mb-4">
              <View className="flex-row items-center">
                <View className="w-10 h-10 bg-blue-100 rounded-full justify-center items-center mr-3">
                  <Ionicons name="download" size={20} color="#3B82F6" />
                </View>
                <View>
                  <Text className="text-gray-900 font-semibold">{totalFiles} Downloaded Files</Text>
                  <Text className="text-gray-500 text-sm">Tap to view all downloads</Text>
                </View>
              </View>
              <TouchableOpacity
                onPress={() => router.push('/downloads')}
                className="p-2"
              >
                <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
              </TouchableOpacity>
            </View>

            {recentDownloads.length > 0 && (
              <>
                <View className="border-t border-gray-100 pt-3">
                  <Text className="text-gray-600 font-medium text-sm mb-3">Recent Downloads</Text>
                  {recentDownloads.map((file, index) => (
                    <View key={`${file.path}-${index}`} className="flex-row items-center py-2">
                      <Ionicons
                        name={getFileIcon(file.type, file.name) as any}
                        size={16}
                        color={
                          file.type === 'pdf' ? '#DC2626' :
                          file.type === 'image' ? '#059669' :
                          file.type === 'document' ? '#2B579A' :
                          '#6B7280'
                        }
                        className="mr-3"
                      />
                      <View className="flex-1">
                        <Text className="text-gray-900 text-sm font-medium" numberOfLines={1}>
                          {file.name}
                        </Text>
                        <Text className="text-gray-500 text-xs">
                          {file.category} • {formatFileSize(file.size)}
                        </Text>
                      </View>
                      <Text className="text-gray-400 text-xs">{file.dateDownloaded}</Text>
                    </View>
                  ))}
                </View>
              </>
            )}
          </>
        )}
      </View>
    </View>
  );
};

export default DownloadsSummary;
