import { Ionicons } from "@expo/vector-icons";
import { Tabs } from "expo-router";
import { Image, ImageSourcePropType, View } from "react-native";

const TabIcon = ({
  source,
  focused,
}: {
  source: ImageSourcePropType;
  focused: boolean;
}) => (
  <View
    className={`flex p-4 h-16 w-16 justify-center items-center ${focused ? "bg-white rounded-full" : ""}`}
  >
    <Image
      source={source}
      tintColor={focused ? "black" : "white"}
      resizeMode="contain"
      className="w-7 h-7"
    />
  </View>
);

const IconTabIcon = ({
  name,
  focused,
}: {
  name: keyof typeof Ionicons.glyphMap;
  focused: boolean;
}) => (
  <View
    className={`flex p-4 h-16 w-16 justify-center items-center ${focused ? "bg-white rounded-full" : ""}`}
  >
    <Ionicons
      name={name}
      size={28}
      color={focused ? "black" : "white"}
    />
  </View>
);

export default function Layout() {
  return (
    <Tabs
      initialRouteName="home"
      screenOptions={{
        tabBarActiveTintColor: "white",
        tabBarInactiveTintColor: "white",
        tabBarShowLabel: false,
        tabBarStyle: {
          backgroundColor: "#1d1d1d",
          borderRadius: 50,
          overflow: "hidden",
          marginHorizontal: 20,
          marginBottom: 20,
          height: 78,
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          display: "flex",
          alignItems: "center",
          paddingTop: 17,
        },
        tabBarItemStyle: {
          alignItems: "center",
          justifyContent: "center",
        },
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) => (
            <TabIcon source={require('../../../assets/images/Home.png')} focused={focused} />
          ),
        }}
      />
      <Tabs.Screen
        name="category"
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) => (
            <TabIcon source={require('../../../assets/images/Category.png')} focused={focused} />
          ),
        }}
      />
      <Tabs.Screen
        name="leaderboard"
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) => (
            <TabIcon source={require('../../../assets/images/Chart.png')} focused={focused} />
          ),
        }}
      />

      <Tabs.Screen
        name="downloads"
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) => (
            <IconTabIcon name="download" focused={focused} />
          ),
        }}
      />

      <Tabs.Screen
        name="profile"
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) => (
            <TabIcon source={require('../../../assets/images/Profile.png')} focused={focused} />
          ),
        }}
      />
    </Tabs>
  );
}