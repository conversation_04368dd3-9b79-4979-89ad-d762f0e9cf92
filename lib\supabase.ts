import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';
import Constants from 'expo-constants';

const supabaseUrl = "https://fvwojlqvqvnbxgnvyxju.supabase.co";
const supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ2d29qbHF2cXZuYnhnbnZ5eGp1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwNzg1NTUsImV4cCI6MjA2NDY1NDU1NX0.sToGvYG1wYinjdObNZucvblTBIc6eDRwGNTefkshzJY";

console.log('Supabase Config:', {
  url: supabaseUrl ? 'Present' : 'Missing',
  key: supabaseAnonKey ? 'Present' : 'Missing',
  config: Constants.expoConfig?.extra
});

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error(
    'Missing Supabase environment variables. Please check your .env file and make sure EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY are set correctly.'
  );
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  global: {
    headers: {
      'x-application-name': 'travelapp'
    }
  }
});

// Enhanced storage verification - only run when user is authenticated
export const verifyStorageAccess = async () => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.log('No authenticated user, skipping storage verification');
      return false;
    }

    console.log('Verifying storage access for authenticated user...');
    
    // First, list all buckets to see what's available
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.error('Error listing buckets:', bucketsError);
      return false;
    }
    
    console.log('Available buckets:', buckets?.map(b => b.name));
    
    // Check if avatars bucket exists
    const avatarsBucket = buckets?.find(b => b.name === 'avatars');
    if (!avatarsBucket) {
      console.error('Avatars bucket not found in available buckets');
      return false;
    }
    
    // Try to access the avatars bucket specifically
    const { data: bucketData, error: bucketError } = await supabase.storage.getBucket('avatars');
    
    if (bucketError) {
      console.error('Storage access error:', bucketError);
      return false;
    }
    
    console.log('Storage access verified:', bucketData);
    return true;
    
  } catch (error) {
    console.error('Storage verification failed:', error);
    return false;
  }
};
