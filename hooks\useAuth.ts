import { Session, User } from '@supabase/supabase-js';
import { useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';
import { AuthService } from '../services/AuthService';
import { SignUpData, UserProfile } from '../types';

interface UseAuthReturn {
  user: User | null;
  profile: UserProfile | null;
  session: Session | null;
  loading: boolean;
  error: string | null;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (data: SignUpData) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  refreshProfile: () => Promise<void>;
}

export const useAuth = (): UseAuthReturn => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      if (session?.user) {
        fetchProfile(session.user.id);
      } else {
        setLoading(false);
      }
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);
        setSession(session);
        setUser(session?.user ?? null);
        
        if (session?.user) {
          await fetchProfile(session.user.id);
        } else {
          setProfile(null);
          setLoading(false);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const fetchProfile = async (userId: string) => {
    try {
      setError(null);
      console.log('Fetching profile for user:', userId);
      
      const profileData = await AuthService.getUserProfile(userId);
      
      if (!profileData) {
        console.warn('No profile found for user:', userId);
        // You might want to create a profile here if it doesn't exist
        // or redirect to a profile setup screen
      } else {
        console.log('Profile fetched successfully:', profileData.username);
        setProfile(profileData);
      }
    } catch (err) {
      console.error('Error fetching profile:', err);
      setError((err as Error).message);
      // Don't throw the error, just log it and continue
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await AuthService.signIn(email, password);
      
      if (data.user) {
        // Profile will be fetched automatically by the auth state change listener
        console.log('User signed in successfully:', data.user.id);
      }
    } catch (err) {
      console.error('Sign in error:', err);
      setError((err as Error).message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (data: SignUpData) => {
    try {
      setLoading(true);
      setError(null);
      await AuthService.signUp(data);
    } catch (err) {
      console.error('Sign up error:', err);
      setError((err as Error).message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      setError(null);
      await AuthService.signOut();
      setProfile(null); // Clear profile on sign out
    } catch (err) {
      console.error('Sign out error:', err);
      setError((err as Error).message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) return;
    
    try {
      setError(null);
      const updatedProfile = await AuthService.updateProfile(user.id, updates);
      setProfile(updatedProfile);
    } catch (err) {
      console.error('Update profile error:', err);
      setError((err as Error).message);
      throw err;
    }
  };

  const refreshProfile = async () => {
    if (!user) return;
    await fetchProfile(user.id);
  };

  return {
    user,
    profile,
    session,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    updateProfile,
    refreshProfile,
  };
};