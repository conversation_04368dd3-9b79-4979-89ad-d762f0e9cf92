import { Ionicons } from '@expo/vector-icons';
import * as FileSystem from 'expo-file-system';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface DownloadedFile {
  name: string;
  path: string;
  category: string;
  section: string;
  size: number;
  dateDownloaded: string;
  type: 'pdf' | 'image' | 'document' | 'other';
}

const DownloadsScreen = () => {
  const [downloadedFiles, setDownloadedFiles] = useState<DownloadedFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDownloadedFiles();
  }, []);

  const loadDownloadedFiles = async () => {
    try {
      setLoading(true);
      setError(null);

      const appName = 'quizzy';
      const downloadsPath = `${FileSystem.documentDirectory}${appName}/courses`;
      
      const dirInfo = await FileSystem.getInfoAsync(downloadsPath);
      if (!dirInfo.exists) {
        setDownloadedFiles([]);
        return;
      }

      const files: DownloadedFile[] = [];
      
      // Get all categories
      const categories = await FileSystem.readDirectoryAsync(downloadsPath);
      
      for (const category of categories) {
        const categoryPath = `${downloadsPath}/${category}`;
        const categoryInfo = await FileSystem.getInfoAsync(categoryPath);
        
        if (categoryInfo.isDirectory) {
          // Get all sections in this category
          const sections = await FileSystem.readDirectoryAsync(categoryPath);
          
          for (const section of sections) {
            const sectionPath = `${categoryPath}/${section}`;
            const sectionInfo = await FileSystem.getInfoAsync(sectionPath);
            
            if (sectionInfo.isDirectory) {
              // Get all files in this section
              const sectionFiles = await FileSystem.readDirectoryAsync(sectionPath);
              
              for (const fileName of sectionFiles) {
                const filePath = `${sectionPath}/${fileName}`;
                const fileInfo = await FileSystem.getInfoAsync(filePath);
                
                if (!fileInfo.isDirectory) {
                  files.push({
                    name: fileName,
                    path: filePath,
                    category: category.replace(/_/g, ' ').toUpperCase(),
                    section: section.charAt(0).toUpperCase() + section.slice(1),
                    size: (fileInfo as any).size || 0,
                    dateDownloaded: new Date((fileInfo as any).modificationTime || Date.now()).toLocaleDateString(),
                    type: getFileType(fileName),
                  });
                }
              }
            }
          }
        }
      }

      // Sort by date downloaded (newest first)
      files.sort((a, b) => new Date(b.dateDownloaded).getTime() - new Date(a.dateDownloaded).getTime());
      
      setDownloadedFiles(files);
    } catch (err) {
      console.error('Error loading downloaded files:', err);
      setError('Failed to load downloaded files');
    } finally {
      setLoading(false);
    }
  };

  const getFileType = (fileName: string): 'pdf' | 'image' | 'document' | 'other' => {
    const extension = fileName.toLowerCase().split('.').pop();
    
    if (extension === 'pdf') return 'pdf';
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(extension || '')) return 'image';
    if (['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'].includes(extension || '')) return 'document';
    return 'other';
  };

  const getFileIcon = (type: string, fileName?: string) => {
    switch (type) {
      case 'pdf':
        return 'document-text';
      case 'image':
        return 'image';
      case 'document':
        const ext = fileName?.toLowerCase().split('.').pop();
        if (['ppt', 'pptx'].includes(ext || '')) return 'easel';
        if (['xls', 'xlsx'].includes(ext || '')) return 'grid';
        return 'document-text';
      default:
        return 'document';
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const handleFilePress = async (file: DownloadedFile) => {
    try {
      Alert.alert(
        'File Downloaded',
        `${file.name} is saved in your app's storage. You can access it through your device's file manager.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error opening file:', error);
      Alert.alert('Error', 'Could not access the file.');
    }
  };

  const handleDeleteFile = async (file: DownloadedFile) => {
    Alert.alert(
      'Delete File',
      `Are you sure you want to delete "${file.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await FileSystem.deleteAsync(file.path);
              setDownloadedFiles(prev => prev.filter(f => f.path !== file.path));
              Alert.alert('Success', 'File deleted successfully');
            } catch (error) {
              console.error('Error deleting file:', error);
              Alert.alert('Error', 'Could not delete the file');
            }
          }
        }
      ]
    );
  };

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 justify-center items-center">
          <Text className="text-lg">Loading downloads...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center p-4 border-b border-gray-200">
        <TouchableOpacity
          onPress={() => router.back()}
          className="mr-4"
        >
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <View className="flex-1">
          <Text className="text-xl font-bold">Downloads</Text>
          <Text className="text-gray-600 text-sm">
            {downloadedFiles.length} file{downloadedFiles.length !== 1 ? 's' : ''}
          </Text>
        </View>
        <TouchableOpacity
          onPress={loadDownloadedFiles}
          className="p-2"
        >
          <Ionicons name="refresh" size={24} color="#2563EB" />
        </TouchableOpacity>
      </View>

      <ScrollView className="flex-1">
        {error ? (
          <View className="flex-1 justify-center items-center p-4">
            <Text className="text-red-500 text-center mb-4">{error}</Text>
            <TouchableOpacity
              onPress={loadDownloadedFiles}
              className="bg-blue-500 px-6 py-3 rounded-lg"
            >
              <Text className="text-white font-semibold">Retry</Text>
            </TouchableOpacity>
          </View>
        ) : downloadedFiles.length === 0 ? (
          <View className="flex-1 justify-center items-center p-8">
            <Ionicons name="download-outline" size={64} color="#9CA3AF" />
            <Text className="text-gray-500 text-center mt-4 text-lg">
              No downloads yet
            </Text>
            <Text className="text-gray-400 text-center mt-2">
              Downloaded files will appear here
            </Text>
          </View>
        ) : (
          <View className="p-4">
            {downloadedFiles.map((file, index) => (
              <View
                key={`${file.path}-${index}`}
                className="bg-gray-50 rounded-lg mb-3 overflow-hidden"
              >
                <TouchableOpacity
                  onPress={() => handleFilePress(file)}
                  className="p-4"
                >
                  <View className="flex-row items-center">
                    <Ionicons
                      name={getFileIcon(file.type, file.name) as any}
                      size={24}
                      color={
                        file.type === 'pdf' ? '#DC2626' :
                        file.type === 'image' ? '#059669' :
                        file.type === 'document' ? '#2B579A' :
                        '#6B7280'
                      }
                      className="mr-3"
                    />
                    <View className="flex-1">
                      <Text className="font-medium text-gray-900" numberOfLines={1}>
                        {file.name}
                      </Text>
                      <Text className="text-sm text-gray-500 mt-1">
                        {file.category} • {file.section}
                      </Text>
                      <Text className="text-xs text-gray-400 mt-1">
                        {formatFileSize(file.size)} • Downloaded {file.dateDownloaded}
                      </Text>
                    </View>
                    <TouchableOpacity
                      onPress={() => handleDeleteFile(file)}
                      className="p-2 ml-2"
                    >
                      <Ionicons name="trash-outline" size={20} color="#DC2626" />
                    </TouchableOpacity>
                  </View>
                </TouchableOpacity>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default DownloadsScreen;
