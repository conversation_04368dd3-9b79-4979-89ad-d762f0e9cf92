import { useEffect, useState } from 'react';
import { QuizService } from '../services/QuizService';
import { Quiz, QuizCategory } from '../types';

export const useQuizzes = (categoryId?: string) => {
  const [quizzes, setQuizzes] = useState<Quiz[]>([]);
  const [categories, setCategories] = useState<QuizCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchQuizzes = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [quizzesData, categoriesData] = await Promise.all([
        categoryId 
          ? QuizService.getQuizzesByCategory(categoryId)
          : QuizService.getFeaturedQuizzes(),
        QuizService.getCategories(),
      ]);
      
      setQuizzes(quizzesData);
      setCategories(categoriesData);
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const searchQuizzes = async (query: string, categoryFilter?: string) => {
    try {
      setLoading(true);
      const results = await QuizService.searchQuizzes(query, categoryFilter);
      setQuizzes(results);
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQuizzes();
  }, [categoryId]);

  return {
    quizzes,
    categories,
    loading,
    error,
    refetch: fetchQuizzes,
    searchQuizzes,
  };
};
