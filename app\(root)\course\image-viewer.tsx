import { router, useLocalSearchParams } from 'expo-router';
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Image, ScrollView, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

const ImageViewer = () => {
  const { url, fileName } = useLocalSearchParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });

  const urlStr = Array.isArray(url) ? url[0] : url;
  const fileNameStr = Array.isArray(fileName) ? fileName[0] : fileName;

  const screenWidth = Dimensions.get('window').width;
  const screenHeight = Dimensions.get('window').height;

  const handleImageLoad = () => {
    setLoading(false);
    
    // Get image dimensions
    Image.getSize(
      urlStr || '',
      (width, height) => {
        setImageSize({ width, height });
      },
      (error) => {
        console.warn('Could not get image size:', error);
      }
    );
  };

  const handleImageError = () => {
    setLoading(false);
    setError('Failed to load image');
  };

  // Calculate display dimensions while maintaining aspect ratio
  const getDisplayDimensions = () => {
    if (imageSize.width === 0 || imageSize.height === 0) {
      return { width: screenWidth - 32, height: 300 };
    }

    const aspectRatio = imageSize.width / imageSize.height;
    const maxWidth = screenWidth - 32;
    const maxHeight = screenHeight * 0.7;

    let displayWidth = maxWidth;
    let displayHeight = displayWidth / aspectRatio;

    if (displayHeight > maxHeight) {
      displayHeight = maxHeight;
      displayWidth = displayHeight * aspectRatio;
    }

    return { width: displayWidth, height: displayHeight };
  };

  const displayDimensions = getDisplayDimensions();

  return (
    <SafeAreaView className="flex-1 bg-black">
      {/* Header */}
      <View className="flex-row items-center p-4 bg-black">
        <TouchableOpacity
          onPress={() => router.back()}
          className="mr-4"
        >
          <Ionicons name="arrow-back" size={24} color="#FFF" />
        </TouchableOpacity>
        <View className="flex-1">
          <Text className="text-lg font-semibold text-white" numberOfLines={1}>
            {fileNameStr}
          </Text>
          {imageSize.width > 0 && (
            <Text className="text-sm text-gray-300">
              {imageSize.width} × {imageSize.height}
            </Text>
          )}
        </View>
      </View>

      {/* Image Viewer */}
      <View className="flex-1 justify-center items-center">
        {loading && (
          <View className="absolute inset-0 justify-center items-center bg-black z-10">
            <Text className="text-white text-lg">Loading image...</Text>
          </View>
        )}
        
        {error ? (
          <View className="flex-1 justify-center items-center p-4">
            <Ionicons name="image-outline" size={64} color="#EF4444" />
            <Text className="text-red-500 text-center mt-4 text-lg">
              {error}
            </Text>
            <TouchableOpacity
              onPress={() => router.back()}
              className="bg-blue-500 px-6 py-3 rounded-lg mt-4"
            >
              <Text className="text-white font-semibold">Go Back</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <ScrollView
            contentContainerStyle={{
              flexGrow: 1,
              justifyContent: 'center',
              alignItems: 'center',
              padding: 16,
            }}
            maximumZoomScale={3}
            minimumZoomScale={0.5}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
          >
            <Image
              source={{ uri: urlStr }}
              style={{
                width: displayDimensions.width,
                height: displayDimensions.height,
              }}
              resizeMode="contain"
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
          </ScrollView>
        )}
      </View>

      {/* Footer with image info */}
      {!loading && !error && imageSize.width > 0 && (
        <View className="bg-black border-t border-gray-800 p-4">
          <View className="flex-row justify-center items-center">
            <Text className="text-gray-300 text-center">
              {imageSize.width} × {imageSize.height} pixels
            </Text>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

export default ImageViewer;
