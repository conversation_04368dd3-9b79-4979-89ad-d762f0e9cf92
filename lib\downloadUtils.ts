import * as FileSystem from 'expo-file-system';
import { Alert, Platform } from 'react-native';

export interface DownloadProgress {
  totalBytesWritten: number;
  totalBytesExpectedToWrite: number;
  progress: number;
}

export interface DownloadOptions {
  url: string;
  fileName: string;
  categoryName: string;
  sectionName: string;
  onProgress?: (progress: DownloadProgress) => void;
}

export class FileDownloadManager {
  private static instance: FileDownloadManager;
  private activeDownloads: Map<string, FileSystem.DownloadResumable> = new Map();

  static getInstance(): FileDownloadManager {
    if (!FileDownloadManager.instance) {
      FileDownloadManager.instance = new FileDownloadManager();
    }
    return FileDownloadManager.instance;
  }

  private async requestPermissions(): Promise<boolean> {
    try {
      // For now, we'll just return true since we're using the app's document directory
      // which doesn't require special permissions
      return true;
    } catch (error) {
      console.error('Permission request failed:', error);
      return false;
    }
  }

  private getDownloadPath(categoryName: string, sectionName: string, fileName: string): string {
    const appName = 'quizzy'; // Using the app name you mentioned
    const sanitizedCategory = categoryName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
    const sanitizedSection = sectionName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');

    if (Platform.OS === 'android') {
      // For Android, we'll use the app's document directory first, then move to Downloads
      return `${FileSystem.documentDirectory}${appName}/courses/${sanitizedCategory}/${sanitizedSection}/${sanitizedFileName}`;
    } else {
      // For iOS, use the document directory
      return `${FileSystem.documentDirectory}${appName}/courses/${sanitizedCategory}/${sanitizedSection}/${sanitizedFileName}`;
    }
  }

  private async ensureDirectoryExists(filePath: string): Promise<void> {
    const directory = filePath.substring(0, filePath.lastIndexOf('/'));
    const dirInfo = await FileSystem.getInfoAsync(directory);
    
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(directory, { intermediates: true });
    }
  }

  async downloadFile(options: DownloadOptions): Promise<string | null> {
    const { url, fileName, categoryName, sectionName, onProgress } = options;
    
    try {
      // Request permissions
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        Alert.alert('Permission Required', 'Storage permission is required to download files.');
        return null;
      }

      // Generate download path
      const downloadPath = this.getDownloadPath(categoryName, sectionName, fileName);
      
      // Ensure directory exists
      await this.ensureDirectoryExists(downloadPath);

      // Check if file already exists
      const fileInfo = await FileSystem.getInfoAsync(downloadPath);
      if (fileInfo.exists) {
        Alert.alert(
          'File Exists',
          'This file has already been downloaded. Do you want to download it again?',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Re-download', 
              onPress: () => this.performDownload(url, downloadPath, fileName, onProgress)
            }
          ]
        );
        return downloadPath;
      }

      return await this.performDownload(url, downloadPath, fileName, onProgress);
    } catch (error) {
      console.error('Download failed:', error);
      Alert.alert('Download Failed', 'An error occurred while downloading the file.');
      return null;
    }
  }

  private async performDownload(
    url: string, 
    downloadPath: string, 
    fileName: string, 
    onProgress?: (progress: DownloadProgress) => void
  ): Promise<string | null> {
    try {
      const downloadResumable = FileSystem.createDownloadResumable(
        url,
        downloadPath,
        {},
        (downloadProgress) => {
          const progress = {
            totalBytesWritten: downloadProgress.totalBytesWritten,
            totalBytesExpectedToWrite: downloadProgress.totalBytesExpectedToWrite,
            progress: downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite
          };
          onProgress?.(progress);
        }
      );

      // Store active download
      this.activeDownloads.set(fileName, downloadResumable);

      const result = await downloadResumable.downloadAsync();
      
      // Remove from active downloads
      this.activeDownloads.delete(fileName);

      if (result) {
        Alert.alert(
          'Download Complete',
          `${fileName} has been downloaded successfully to your app's storage.`,
          [
            { text: 'OK' }
          ]
        );

        return result.uri;
      }
      
      return null;
    } catch (error) {
      this.activeDownloads.delete(fileName);
      throw error;
    }
  }



  cancelDownload(fileName: string): void {
    const download = this.activeDownloads.get(fileName);
    if (download) {
      download.cancelAsync();
      this.activeDownloads.delete(fileName);
    }
  }

  async getDownloadedFiles(categoryName: string, sectionName: string): Promise<string[]> {
    try {
      const appName = 'quizzy';
      const sanitizedCategory = categoryName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
      const sanitizedSection = sectionName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
      
      const directoryPath = `${FileSystem.documentDirectory}${appName}/courses/${sanitizedCategory}/${sanitizedSection}`;
      const dirInfo = await FileSystem.getInfoAsync(directoryPath);
      
      if (dirInfo.exists && dirInfo.isDirectory) {
        const files = await FileSystem.readDirectoryAsync(directoryPath);
        return files;
      }
      
      return [];
    } catch (error) {
      console.error('Error getting downloaded files:', error);
      return [];
    }
  }

  async isFileDownloaded(categoryName: string, sectionName: string, fileName: string): Promise<boolean> {
    try {
      const downloadPath = this.getDownloadPath(categoryName, sectionName, fileName);
      const fileInfo = await FileSystem.getInfoAsync(downloadPath);
      return fileInfo.exists;
    } catch (error) {
      return false;
    }
  }
}

export const downloadManager = FileDownloadManager.getInstance();
