import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import * as Sharing from 'expo-sharing';
import { Alert, Platform } from 'react-native';

export interface DownloadProgress {
  totalBytesWritten: number;
  totalBytesExpectedToWrite: number;
  progress: number;
}

export enum StorageLocation {
  APP_STORAGE = 'app_storage',
  DEVICE_DOWNLOADS = 'device_downloads'
}

export interface DownloadOptions {
  url: string;
  fileName: string;
  categoryName: string;
  sectionName: string;
  storageLocation?: StorageLocation;
  onProgress?: (progress: DownloadProgress) => void;
}

export class FileDownloadManager {
  private static instance: FileDownloadManager;
  private activeDownloads: Map<string, FileSystem.DownloadResumable> = new Map();

  static getInstance(): FileDownloadManager {
    if (!FileDownloadManager.instance) {
      FileDownloadManager.instance = new FileDownloadManager();
    }
    return FileDownloadManager.instance;
  }

  private async requestPermissions(storageLocation: StorageLocation): Promise<boolean> {
    try {
      if (storageLocation === StorageLocation.APP_STORAGE) {
        // App storage doesn't require special permissions
        return true;
      }

      if (Platform.OS === 'android' && storageLocation === StorageLocation.DEVICE_DOWNLOADS) {
        // Request media library permissions for device downloads
        const { status } = await MediaLibrary.requestPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(
            'Permission Required',
            'Storage permission is required to save files to your device Downloads folder. You can still save to app storage without this permission.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Grant Permission', onPress: () => MediaLibrary.requestPermissionsAsync() }
            ]
          );
          return false;
        }
        return true;
      }

      // iOS doesn't need special permissions for sharing
      return true;
    } catch (error) {
      console.error('Permission request failed:', error);
      Alert.alert(
        'Permission Error',
        'Failed to request storage permissions. Files will be saved to app storage instead.'
      );
      return false;
    }
  }

  private getDownloadPath(
    categoryName: string,
    sectionName: string,
    fileName: string,
    storageLocation: StorageLocation
  ): string {
    const appName = 'quizzy';
    const sanitizedCategory = categoryName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
    const sanitizedSection = sectionName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');

    if (storageLocation === StorageLocation.APP_STORAGE) {
      // App's private document directory
      return `${FileSystem.documentDirectory}${appName}/courses/${sanitizedCategory}/${sanitizedSection}/${sanitizedFileName}`;
    } else {
      // Temporary path for device downloads (will be moved to Downloads folder)
      return `${FileSystem.cacheDirectory}${appName}_temp/${sanitizedFileName}`;
    }
  }

  private async ensureDirectoryExists(filePath: string): Promise<void> {
    const directory = filePath.substring(0, filePath.lastIndexOf('/'));
    const dirInfo = await FileSystem.getInfoAsync(directory);

    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(directory, { intermediates: true });
    }
  }

  async downloadFile(options: DownloadOptions): Promise<string | null> {
    const { url, fileName, categoryName, sectionName, storageLocation = StorageLocation.APP_STORAGE, onProgress } = options;

    try {
      // Request permissions based on storage location
      const hasPermission = await this.requestPermissions(storageLocation);
      if (!hasPermission && storageLocation === StorageLocation.DEVICE_DOWNLOADS) {
        // Fallback to app storage if device downloads permission denied
        Alert.alert(
          'Permission Denied',
          'File will be saved to app storage instead of device Downloads folder.',
          [{ text: 'OK' }]
        );
        return await this.downloadFile({
          ...options,
          storageLocation: StorageLocation.APP_STORAGE
        });
      }

      // Generate download path
      const downloadPath = this.getDownloadPath(categoryName, sectionName, fileName, storageLocation);

      // Ensure directory exists
      await this.ensureDirectoryExists(downloadPath);

      // Check if file already exists
      const fileInfo = await FileSystem.getInfoAsync(downloadPath);
      if (fileInfo.exists) {
        Alert.alert(
          'File Exists',
          'This file has already been downloaded. Do you want to download it again?',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Re-download',
              onPress: () => this.performDownload(url, downloadPath, fileName, categoryName, sectionName, storageLocation, onProgress)
            }
          ]
        );
        return downloadPath;
      }

      return await this.performDownload(url, downloadPath, fileName, categoryName, sectionName, storageLocation, onProgress);
    } catch (error) {
      console.error('Download failed:', error);
      Alert.alert('Download Failed', 'An error occurred while downloading the file.');
      return null;
    }
  }

  private async performDownload(
    url: string,
    downloadPath: string,
    fileName: string,
    categoryName: string,
    sectionName: string,
    storageLocation: StorageLocation,
    onProgress?: (progress: DownloadProgress) => void
  ): Promise<string | null> {
    try {
      const downloadResumable = FileSystem.createDownloadResumable(
        url,
        downloadPath,
        {},
        (downloadProgress) => {
          const progress = {
            totalBytesWritten: downloadProgress.totalBytesWritten,
            totalBytesExpectedToWrite: downloadProgress.totalBytesExpectedToWrite,
            progress: downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite
          };
          onProgress?.(progress);
        }
      );

      // Store active download
      this.activeDownloads.set(fileName, downloadResumable);

      const result = await downloadResumable.downloadAsync();

      // Remove from active downloads
      this.activeDownloads.delete(fileName);

      if (result) {
        let finalPath = result.uri;

        // Handle device downloads
        if (storageLocation === StorageLocation.DEVICE_DOWNLOADS) {
          try {
            finalPath = await this.moveToDeviceDownloads(result.uri, fileName, categoryName, sectionName);
          } catch (error) {
            console.warn('Failed to move to device downloads:', error);
            Alert.alert(
              'Download Complete',
              `${fileName} downloaded to app storage. Could not save to device Downloads folder.`,
              [
                { text: 'OK' },
                { text: 'Open', onPress: () => this.openFile(result.uri) }
              ]
            );
            return result.uri;
          }
        }

        const locationText = storageLocation === StorageLocation.DEVICE_DOWNLOADS
          ? 'device Downloads folder'
          : 'app storage';

        Alert.alert(
          'Download Complete',
          `${fileName} has been downloaded successfully to your ${locationText}.`,
          [
            { text: 'OK' },
            { text: 'Open', onPress: () => this.openFile(finalPath) }
          ]
        );

        return finalPath;
      }

      return null;
    } catch (error) {
      this.activeDownloads.delete(fileName);
      throw error;
    }
  }



  cancelDownload(fileName: string): void {
    const download = this.activeDownloads.get(fileName);
    if (download) {
      download.cancelAsync();
      this.activeDownloads.delete(fileName);
    }
  }

  async getDownloadedFiles(categoryName: string, sectionName: string): Promise<string[]> {
    try {
      const appName = 'quizzy';
      const sanitizedCategory = categoryName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
      const sanitizedSection = sectionName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();

      const directoryPath = `${FileSystem.documentDirectory}${appName}/courses/${sanitizedCategory}/${sanitizedSection}`;
      const dirInfo = await FileSystem.getInfoAsync(directoryPath);

      if (dirInfo.exists && dirInfo.isDirectory) {
        const files = await FileSystem.readDirectoryAsync(directoryPath);
        return files;
      }

      return [];
    } catch (error) {
      console.error('Error getting downloaded files:', error);
      return [];
    }
  }

  async isFileDownloaded(categoryName: string, sectionName: string, fileName: string, storageLocation: StorageLocation = StorageLocation.APP_STORAGE): Promise<boolean> {
    try {
      const downloadPath = this.getDownloadPath(categoryName, sectionName, fileName, storageLocation);
      const fileInfo = await FileSystem.getInfoAsync(downloadPath);
      return fileInfo.exists;
    } catch (error) {
      return false;
    }
  }

  private async moveToDeviceDownloads(
    tempFilePath: string,
    fileName: string,
    categoryName: string,
    sectionName: string
  ): Promise<string> {
    try {
      if (Platform.OS === 'android') {
        // Create asset from temporary file
        const asset = await MediaLibrary.createAssetAsync(tempFilePath);

        // Try to get or create Downloads album
        let album = await MediaLibrary.getAlbumAsync('Downloads');
        if (!album) {
          album = await MediaLibrary.createAlbumAsync('Downloads', asset, false);
        } else {
          await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
        }

        // Clean up temporary file
        await FileSystem.deleteAsync(tempFilePath, { idempotent: true });

        return asset.uri;
      } else {
        // For iOS, we'll use sharing which allows saving to Files app
        return tempFilePath;
      }
    } catch (error) {
      console.error('Error moving to device downloads:', error);
      throw error;
    }
  }

  private async openFile(fileUri: string): Promise<void> {
    try {
      const isAvailable = await Sharing.isAvailableAsync();
      if (isAvailable) {
        await Sharing.shareAsync(fileUri, {
          mimeType: this.getMimeType(fileUri),
          dialogTitle: 'Open with...'
        });
      } else {
        Alert.alert('Cannot Open', 'No app available to open this file type.');
      }
    } catch (error) {
      console.error('Error opening file:', error);
      Alert.alert('Error', 'Could not open the file.');
    }
  }

  private getMimeType(fileName: string): string {
    const extension = fileName.toLowerCase().split('.').pop();

    switch (extension) {
      case 'pdf': return 'application/pdf';
      case 'jpg':
      case 'jpeg': return 'image/jpeg';
      case 'png': return 'image/png';
      case 'gif': return 'image/gif';
      case 'doc': return 'application/msword';
      case 'docx': return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'ppt': return 'application/vnd.ms-powerpoint';
      case 'pptx': return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case 'xls': return 'application/vnd.ms-excel';
      case 'xlsx': return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      default: return 'application/octet-stream';
    }
  }
}

export const downloadManager = FileDownloadManager.getInstance();
