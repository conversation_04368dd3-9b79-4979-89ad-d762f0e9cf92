/**
 * Pastel Color Scheme for E-Learning App
 * Inspired by modern educational interfaces with soft, calming colors
 */

// Primary pastel tints
const tintColorLight = '#7DD3FC'; // Soft blue
const tintColorDark = '#E6E6FA';  // Soft lavender

export const Colors = {
  // Light theme with pastel colors
  light: {
    text: '#1E293B',           // Dark slate for readability
    textSecondary: '#64748B',  // Medium slate
    textMuted: '#94A3B8',      // Light slate
    background: '#FAFBFC',     // Very light blue-gray
    surface: '#FFFFFF',        // Pure white
    surfaceSecondary: '#F8FAFC', // Light surface
    tint: tintColorLight,
    icon: '#64748B',
    tabIconDefault: '#94A3B8',
    tabIconSelected: tintColorLight,
    border: '#E2E8F0',
    borderLight: '#F1F5F9',
    shadow: 'rgba(148, 163, 184, 0.1)',
  },

  // Dark theme with muted pastels
  dark: {
    text: '#F1F5F9',
    textSecondary: '#CBD5E1',
    textMuted: '#94A3B8',
    background: '#0F172A',
    surface: '#1E293B',
    surfaceSecondary: '#334155',
    tint: tintColorDark,
    icon: '#94A3B8',
    tabIconDefault: '#64748B',
    tabIconSelected: tintColorDark,
    border: '#334155',
    borderLight: '#475569',
    shadow: 'rgba(0, 0, 0, 0.3)',
  },

  // Primary color palette
  primary: {
    50: '#F0F9FF',   // Very light blue
    100: '#E0F2FE',  // Light blue
    200: '#BAE6FD',  // Soft blue
    300: '#7DD3FC',  // Medium blue (main tint)
    400: '#38BDF8',  // Bright blue
    500: '#0EA5E9',  // Main blue
    600: '#0284C7',  // Dark blue
    700: '#0369A1',  // Darker blue
    800: '#075985',  // Deep blue
    900: '#0C4A6E',  // Darkest blue
  },

  // Pastel accent colors
  pastels: {
    lavender: '#E6E6FA',    // Soft lavender
    mint: '#F0FFF0',        // Mint green
    peach: '#FFEEE6',       // Soft peach
    rose: '#FFE4E6',        // Pastel rose
    cream: '#FFF8DC',       // Cream
    sky: '#E0F6FF',         // Sky blue
    lilac: '#F3E8FF',       // Light lilac
    sage: '#F0F4F0',        // Sage green
    powder: '#F0F8FF',      // Powder blue
    blush: '#FFF0F5',       // Blush pink
  },

  // Category colors for subjects
  categories: {
    math: {
      background: '#FEF3C7',    // Soft yellow
      accent: '#F59E0B',        // Warm orange
      text: '#92400E',          // Dark orange
      light: '#FFFBEB',         // Very light yellow
    },
    science: {
      background: '#DCFCE7',    // Soft green
      accent: '#22C55E',        // Green
      text: '#166534',          // Dark green
      light: '#F0FDF4',         // Very light green
    },
    language: {
      background: '#FECACA',    // Soft red
      accent: '#EF4444',        // Red
      text: '#991B1B',          // Dark red
      light: '#FEF2F2',         // Very light red
    },
    history: {
      background: '#E0E7FF',    // Soft indigo
      accent: '#6366F1',        // Indigo
      text: '#3730A3',          // Dark indigo
      light: '#EEF2FF',         // Very light indigo
    },
    art: {
      background: '#FCE7F3',    // Soft pink
      accent: '#EC4899',        // Pink
      text: '#BE185D',          // Dark pink
      light: '#FDF2F8',         // Very light pink
    },
    technology: {
      background: '#E0F2FE',    // Soft cyan
      accent: '#06B6D4',        // Cyan
      text: '#0E7490',          // Dark cyan
      light: '#F0F9FF',         // Very light cyan
    },
  },

  // Status colors with pastel variants
  status: {
    success: {
      light: '#DCFCE7',         // Light green
      main: '#22C55E',          // Green
      dark: '#16A34A',          // Dark green
      background: '#F0FDF4',    // Very light green
    },
    warning: {
      light: '#FEF3C7',         // Light yellow
      main: '#F59E0B',          // Orange
      dark: '#D97706',          // Dark orange
      background: '#FFFBEB',    // Very light yellow
    },
    error: {
      light: '#FEE2E2',         // Light red
      main: '#EF4444',          // Red
      dark: '#DC2626',          // Dark red
      background: '#FEF2F2',    // Very light red
    },
    info: {
      light: '#DBEAFE',         // Light blue
      main: '#3B82F6',          // Blue
      dark: '#2563EB',          // Dark blue
      background: '#EFF6FF',    // Very light blue
    },
  },

  // Interactive element colors
  interactive: {
    primary: {
      default: '#7DD3FC',       // Soft blue
      hover: '#38BDF8',         // Brighter blue
      pressed: '#0EA5E9',       // Darker blue
      disabled: '#CBD5E1',      // Light gray
    },
    secondary: {
      default: '#F1F5F9',       // Light gray
      hover: '#E2E8F0',         // Medium gray
      pressed: '#CBD5E1',       // Darker gray
      disabled: '#F8FAFC',      // Very light gray
    },
    accent: {
      default: '#EC4899',       // Pink
      hover: '#DB2777',         // Darker pink
      pressed: '#BE185D',       // Even darker pink
      disabled: '#FECACA',      // Light pink
    },
  },

  // Card and surface colors
  cards: {
    default: '#FFFFFF',         // White
    elevated: '#FAFBFC',        // Very light gray
    tinted: {
      blue: '#F0F9FF',          // Tinted blue
      green: '#F0FDF4',         // Tinted green
      yellow: '#FFFBEB',        // Tinted yellow
      purple: '#FAF5FF',        // Tinted purple
      pink: '#FDF2F8',          // Tinted pink
      orange: '#FFF7ED',        // Tinted orange
    },
  },

  // Progress and achievement colors
  progress: {
    track: '#F1F5F9',          // Light track
    fill: '#7DD3FC',           // Soft blue fill
    complete: '#22C55E',       // Green complete
    milestone: '#F59E0B',      // Orange milestone
    background: '#F8FAFC',     // Progress background
  },

  // Leaderboard specific colors
  leaderboard: {
    gold: '#FFD700',           // Gold
    silver: '#C0C0C0',         // Silver
    bronze: '#CD7F32',         // Bronze
    rank: '#64748B',           // Regular rank
    background: {
      gold: '#FFFBEB',         // Light gold background
      silver: '#F8FAFC',       // Light silver background
      bronze: '#FFF7ED',       // Light bronze background
      regular: '#FFFFFF',      // Regular background
    },
  },
};

// Helper functions for easier color usage
export const getCategoryColor = (category: string) => {
  const categoryKey = category.toLowerCase() as keyof typeof Colors.categories;
  return Colors.categories[categoryKey] || Colors.categories.technology;
};

export const getStatusColor = (status: 'success' | 'warning' | 'error' | 'info') => {
  return Colors.status[status];
};

export const withOpacity = (color: string, opacity: number) => {
  // Convert opacity (0-1) to hex (00-FF)
  const alpha = Math.round(opacity * 255).toString(16).padStart(2, '0');
  return `${color}${alpha}`;
};

// Gradient combinations for backgrounds
export const gradients = {
  blueToLavender: [Colors.primary[100], Colors.pastels.lavender],
  peachToRose: [Colors.pastels.peach, Colors.pastels.rose],
  mintToSky: [Colors.pastels.mint, Colors.pastels.sky],
  creamToLilac: [Colors.pastels.cream, Colors.pastels.lilac],
  softBlue: [Colors.primary[50], Colors.primary[200]],
  warmPeach: [Colors.pastels.cream, Colors.pastels.peach],
  coolMint: [Colors.pastels.sage, Colors.pastels.mint],
  gentleLavender: [Colors.pastels.powder, Colors.pastels.lavender],
};

// Common color combinations for UI elements
export const combinations = {
  primaryCard: {
    background: Colors.cards.default,
    border: Colors.light.borderLight,
    text: Colors.light.text,
    accent: Colors.primary[300],
  },
  secondaryCard: {
    background: Colors.cards.elevated,
    border: Colors.light.border,
    text: Colors.light.textSecondary,
    accent: Colors.pastels.lavender,
  },
  accentCard: {
    background: Colors.pastels.sky,
    border: Colors.primary[200],
    text: Colors.primary[800],
    accent: Colors.primary[500],
  },
};

export default Colors;
