/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all of your component files.
  content: ["./App.tsx", "./components/**/*.{js,jsx,ts,tsx}", "./app/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      fontFamily: {
        HelveticaNeueBlack: ["HelveticaNeueBlack", "sans-serif"],
        HelveticaNeueBold: ["HelveticaNeueBold", "sans-serif"],
        HelveticaNeueHeavy: ["HelveticaNeueHeavy", "sans-serif"],
        HelveticaNeueLight: ["HelveticaNeueLight", "sans-serif"],
        HelveticaNeueMedium: ["HelveticaNeueMedium", "sans-serif"],
        HelveticaNeueRoman: ["HelveticaNeueRoman", "sans-serif"],
      },
      colors: {
        // Primary pastel colors
        primary: {
          50: '#F0F9FF',
          100: '#E0F2FE',
          200: '#BAE6FD',
          300: '#7DD3FC',
          400: '#38BDF8',
          500: '#0EA5E9',
          600: '#0284C7',
          700: '#0369A1',
          800: '#075985',
          900: '#0C4A6E',
        },
        // Pastel accent colors
        pastel: {
          lavender: '#E6E6FA',
          mint: '#F0FFF0',
          peach: '#FFEEE6',
          rose: '#FFE4E6',
          cream: '#FFF8DC',
          sky: '#E0F6FF',
          lilac: '#F3E8FF',
          sage: '#F0F4F0',
          powder: '#F0F8FF',
          blush: '#FFF0F5',
        },
        // Category colors
        math: {
          light: '#FFFBEB',
          DEFAULT: '#FEF3C7',
          accent: '#F59E0B',
          text: '#92400E',
        },
        science: {
          light: '#F0FDF4',
          DEFAULT: '#DCFCE7',
          accent: '#22C55E',
          text: '#166534',
        },
        language: {
          light: '#FEF2F2',
          DEFAULT: '#FECACA',
          accent: '#EF4444',
          text: '#991B1B',
        },
        history: {
          light: '#EEF2FF',
          DEFAULT: '#E0E7FF',
          accent: '#6366F1',
          text: '#3730A3',
        },
        art: {
          light: '#FDF2F8',
          DEFAULT: '#FCE7F3',
          accent: '#EC4899',
          text: '#BE185D',
        },
        technology: {
          light: '#F0F9FF',
          DEFAULT: '#E0F2FE',
          accent: '#06B6D4',
          text: '#0E7490',
        },
        // UI colors
        surface: {
          DEFAULT: '#FFFFFF',
          secondary: '#F8FAFC',
          elevated: '#FAFBFC',
        },
        border: {
          light: '#F1F5F9',
          DEFAULT: '#E2E8F0',
        },
        text: {
          primary: '#1E293B',
          secondary: '#64748B',
          muted: '#94A3B8',
        },
      },
      backgroundColor: {
        'gradient-blue': 'linear-gradient(135deg, #F0F9FF 0%, #BAE6FD 100%)',
        'gradient-peach': 'linear-gradient(135deg, #FFF8DC 0%, #FFEEE6 100%)',
        'gradient-mint': 'linear-gradient(135deg, #F0F4F0 0%, #F0FFF0 100%)',
        'gradient-lavender': 'linear-gradient(135deg, #F0F8FF 0%, #E6E6FA 100%)',
      },
    },
  },
  plugins: [],
}