export interface UserProfile {
  id: string
  username: string
  display_name?: string
  email: string
  avatar_url?: string
  bio?: string
  country?: string
  timezone?: string
  total_score: number
  games_played: number
  games_won: number
  current_streak: number
  best_streak: number
  total_questions_answered: number
  correct_answers: number
  experience_points: number
  level: number
  coins: number
  preferred_categories: string[]
  notification_preferences: {
    game_invites: boolean
    friend_requests: boolean
    achievements: boolean
  }
  privacy_settings: {
    profile_visible: boolean
    stats_visible: boolean
  }
  created_at: string
  updated_at: string
  last_active_at: string
}

export interface SignUpData {
  email: string
  password: string
  username: string
}

export interface Quiz {
  id: string;
  title: string;
  description: string;
  category_id: string;
  difficulty: 'easy' | 'medium' | 'hard';
  total_questions: number;
  estimated_duration: number;
  is_published: boolean;
  is_featured: boolean;
  times_played: number;
  average_score: number;
}

export interface Question {
  id: string;
  quiz_id: string;
  question_text: string;
  question_type: 'multiple_choice' | 'true_false' | 'text_input';
  options?: string[];
  correct_answer: string;
  base_points: number;
  time_limit: number;
  difficulty: 'easy' | 'medium' | 'hard';
  question_order: number;
}

export interface QuizCategory {
  id: string;
  name: string;
  description: string;
  icon_url?: string;
  color_hex?: string;
}

export interface GameSession {
  id: string;
  quiz_id: string;
  max_players: number;
  current_players: number;
  game_mode: string;
  is_private: boolean;
  invite_code?: string;
  status: 'waiting' | 'active' | 'paused' | 'finished' | 'cancelled';
  current_question_index: number;
  question_start_time?: string;
  settings: any;
  created_at: string;
  started_at?: string;
  finished_at?: string;
}

export interface GameParticipant {
  id: string;
  game_id: string;
  user_id: string;
  user?: UserProfile;
  is_ready: boolean;
  is_connected: boolean;
  current_score: number;
  questions_answered: number;
  correct_answers: number;
  current_streak: number;
  joined_at: string;
}

export interface GameAnswer {
  id: string;
  game_id: string;
  user_id: string;
  question_id: string;
  submitted_answer: string;
  is_correct: boolean;
  points_earned: number;
  time_taken: number;
  answered_at: string;
}

export interface Friendship {
  id: string;
  requester_id: string;
  addressee_id: string;
  status: 'pending' | 'accepted' | 'blocked';
  created_at: string;
  updated_at: string;
  requester?: UserProfile;
  addressee?: UserProfile;
}



export interface Friendship {
  id: string;
  requester_id: string;
  addressee_id: string;
  status: 'pending' | 'accepted' | 'declined' | 'blocked';
  created_at: string;
  updated_at: string;
  requester?: UserProfile;
  addressee?: UserProfile;
}

export interface FriendRequest {
  id: string;
  requester_id: string;
  addressee_id: string;
  status: 'pending';
  created_at: string;
  requester: UserProfile;
}

export interface Friend {
  id: string;
  user_id: string;
  friend_id: string;
  friendship_date: string;
  user: UserProfile;
}