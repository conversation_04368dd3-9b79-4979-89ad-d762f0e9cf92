import { useAuth } from '@/hooks/useAuth';
import { FriendService } from '@/services/FriendService';
import { UserProfile } from '@/types';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  Text,
  TouchableOpacity,
  View
} from 'react-native';

const FriendsSection = () => {
  const { user } = useAuth();
  const [friendCount, setFriendCount] = useState(0);
  const [recentFriends, setRecentFriends] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadFriendsData();
    }
  }, [user]);

  const loadFriendsData = async () => {
    if (!user) return;

    try {
      const [count, recent] = await Promise.all([
        FriendService.getFriendCount(user.id),
        FriendService.getRecentFriends(user.id, 5)
      ]);
      
      setFriendCount(count);
      setRecentFriends(recent);
    } catch (error) {
      console.error('Error loading friends data:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderFriendAvatar = (friend: UserProfile, index: number) => (
    <View
      key={friend.id}
      className="relative"
      style={{ marginLeft: index > 0 ? -8 : 0 }}
    >
      {friend.avatar_url ? (
        <Image
          source={{ uri: friend.avatar_url }}
          className="w-10 h-10 rounded-full border-2 border-white"
        />
      ) : (
        <View className="w-10 h-10 rounded-full bg-gray-300 border-2 border-white justify-center items-center">
          <Ionicons name="person" size={16} color="#6B7280" />
        </View>
      )}
    </View>
  );

  if (loading) {
    return (
      <View className="px-6 py-4">
        <View className="bg-white rounded-2xl p-4 shadow-sm">
          <View className="flex-row items-center justify-center py-4">
            <ActivityIndicator size="small" color="#3B82F6" />
            <Text className="text-gray-500 ml-2">Loading friends...</Text>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View className="px-6 py-4">
      <Text className="text-xl font-bold text-gray-900 mb-4">👥 Friends</Text>
      
      <TouchableOpacity
        onPress={() => router.push('/(root)/friends')}
        className="bg-white rounded-2xl p-4 shadow-sm"
      >
        <View className="flex-row items-center justify-between mb-3">
          <View className="flex-row items-center">
            <View className="w-12 h-12 bg-blue-100 rounded-full justify-center items-center mr-3">
              <Ionicons name="people" size={24} color="#3B82F6" />
            </View>
            <View>
              <Text className="text-lg font-bold text-gray-900">{friendCount}</Text>
              <Text className="text-sm text-gray-500">
                {friendCount === 1 ? 'Friend' : 'Friends'}
              </Text>
            </View>
          </View>
          
          <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
        </View>

        {recentFriends.length > 0 ? (
          <View>
            <View className="border-t border-gray-100 pt-3">
              <Text className="text-sm text-gray-600 mb-3">Recent Friends</Text>
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                  {recentFriends.slice(0, 4).map((friend, index) => 
                    renderFriendAvatar(friend, index)
                  )}
                  {recentFriends.length > 4 && (
                    <View
                      className="w-10 h-10 rounded-full bg-gray-200 border-2 border-white justify-center items-center"
                      style={{ marginLeft: -8 }}
                    >
                      <Text className="text-xs font-semibold text-gray-600">
                        +{recentFriends.length - 4}
                      </Text>
                    </View>
                  )}
                </View>
                
                <Text className="text-xs text-blue-600 font-medium">View All</Text>
              </View>
            </View>
          </View>
        ) : friendCount === 0 ? (
          <View className="border-t border-gray-100 pt-3">
            <View className="flex-row items-center justify-center py-3">
              <Ionicons name="person-add" size={20} color="#9CA3AF" />
              <Text className="text-gray-500 ml-2">Add your first friend</Text>
            </View>
          </View>
        ) : null}
      </TouchableOpacity>


    </View>
  );
};

export default FriendsSection;
