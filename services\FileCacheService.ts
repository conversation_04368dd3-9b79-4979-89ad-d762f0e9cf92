

// Conditional import for expo-file-system to handle missing native module
let FileSystem: any = null;
try {
  FileSystem = require('expo-file-system');
} catch (error) {
  console.warn('expo-file-system not available:', error);
}

interface CachedFile {
  id: string;
  fileName: string;
  filePath: string;
  originalUrl: string;
  fileType: string;
  fileSize: number;
  cachedAt: string;
  lastAccessed: string;
}

export class FileCacheService {
  private static readonly MAX_CACHE_SIZE = 500 * 1024 * 1024; // 500MB
  private static readonly CACHE_EXPIRY_DAYS = 30;

  // Dynamic getters for cache paths to avoid accessing FileSystem at class definition time
  private static get CACHE_DIR(): string {
    return FileSystem ? `${FileSystem.documentDirectory}course_cache/` : '';
  }

  private static get CACHE_INDEX_FILE(): string {
    return FileSystem ? `${FileSystem.documentDirectory}cache_index.json` : '';
  }

  // Initialize cache directory
  static async initializeCache(): Promise<void> {
    if (!FileSystem) {
      console.warn('FileSystem not available, cache disabled');
      return;
    }

    try {
      const dirInfo = await FileSystem.getInfoAsync(this.CACHE_DIR);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.CACHE_DIR, { intermediates: true });
      }
    } catch (error) {
      console.error('Error initializing cache:', error);
    }
  }

  // Get cached files index
  static async getCacheIndex(): Promise<CachedFile[]> {
    if (!FileSystem) {
      return [];
    }

    try {
      const indexInfo = await FileSystem.getInfoAsync(this.CACHE_INDEX_FILE);
      if (indexInfo.exists) {
        const indexContent = await FileSystem.readAsStringAsync(this.CACHE_INDEX_FILE);
        return JSON.parse(indexContent);
      }
      return [];
    } catch (error) {
      console.error('Error reading cache index:', error);
      return [];
    }
  }

  // Save cache index
  static async saveCacheIndex(cacheIndex: CachedFile[]): Promise<void> {
    try {
      await FileSystem.writeAsStringAsync(this.CACHE_INDEX_FILE, JSON.stringify(cacheIndex, null, 2));
    } catch (error) {
      console.error('Error saving cache index:', error);
    }
  }

  // Check if file is cached
  static async isFileCached(fileUrl: string): Promise<string | null> {
    const cacheIndex = await this.getCacheIndex();
    const cachedFile = cacheIndex.find(file => file.originalUrl === fileUrl);
    
    if (cachedFile) {
      const fileInfo = await FileSystem.getInfoAsync(cachedFile.filePath);
      if (fileInfo.exists) {
        // Update last accessed time
        cachedFile.lastAccessed = new Date().toISOString();
        await this.saveCacheIndex(cacheIndex);
        return cachedFile.filePath;
      } else {
        // File was deleted, remove from index
        await this.removeCachedFile(fileUrl);
      }
    }
    
    return null;
  }

  // Download and cache file
  static async cacheFile(
    fileUrl: string,
    fileName: string,
    fileType: string,
    onProgress?: (progress: number) => void
  ): Promise<string> {
    if (!FileSystem) {
      throw new Error('File system not available - caching disabled');
    }

    await this.initializeCache();
    
    // Check if already cached
    const cachedPath = await this.isFileCached(fileUrl);
    if (cachedPath) {
      return cachedPath;
    }

    // Clean cache if needed
    await this.cleanCache();

    // Generate unique file path
    const fileId = this.generateFileId(fileUrl);
    const fileExtension = fileName.split('.').pop() || '';
    const cachedFilePath = `${this.CACHE_DIR}${fileId}.${fileExtension}`;

    try {
      // Download file
      const downloadResult = await FileSystem.downloadAsync(
        fileUrl,
        cachedFilePath,
        {
          sessionType: FileSystem.FileSystemSessionType.BACKGROUND,
        }
      );

      if (downloadResult.status === 200) {
        // Get file size
        const fileInfo = await FileSystem.getInfoAsync(cachedFilePath);
        const fileSize = fileInfo.size || 0;

        // Add to cache index
        const cacheIndex = await this.getCacheIndex();
        const newCachedFile: CachedFile = {
          id: fileId,
          fileName,
          filePath: cachedFilePath,
          originalUrl: fileUrl,
          fileType,
          fileSize,
          cachedAt: new Date().toISOString(),
          lastAccessed: new Date().toISOString(),
        };

        cacheIndex.push(newCachedFile);
        await this.saveCacheIndex(cacheIndex);

        return cachedFilePath;
      } else {
        throw new Error(`Download failed with status: ${downloadResult.status}`);
      }
    } catch (error) {
      console.error('Error caching file:', error);
      // Clean up partial download
      const fileInfo = await FileSystem.getInfoAsync(cachedFilePath);
      if (fileInfo.exists) {
        await FileSystem.deleteAsync(cachedFilePath);
      }
      throw error;
    }
  }

  // Remove cached file
  static async removeCachedFile(fileUrl: string): Promise<void> {
    const cacheIndex = await this.getCacheIndex();
    const fileIndex = cacheIndex.findIndex(file => file.originalUrl === fileUrl);
    
    if (fileIndex !== -1) {
      const cachedFile = cacheIndex[fileIndex];
      
      // Delete physical file
      const fileInfo = await FileSystem.getInfoAsync(cachedFile.filePath);
      if (fileInfo.exists) {
        await FileSystem.deleteAsync(cachedFile.filePath);
      }
      
      // Remove from index
      cacheIndex.splice(fileIndex, 1);
      await this.saveCacheIndex(cacheIndex);
    }
  }

  // Clean cache (remove old/large files)
  static async cleanCache(): Promise<void> {
    const cacheIndex = await this.getCacheIndex();
    
    // Calculate total cache size
    let totalSize = 0;
    for (const file of cacheIndex) {
      totalSize += file.fileSize;
    }

    // If cache is too large, remove oldest files
    if (totalSize > this.MAX_CACHE_SIZE) {
      // Sort by last accessed (oldest first)
      cacheIndex.sort((a, b) => new Date(a.lastAccessed).getTime() - new Date(b.lastAccessed).getTime());
      
      while (totalSize > this.MAX_CACHE_SIZE * 0.8 && cacheIndex.length > 0) {
        const oldestFile = cacheIndex.shift()!;
        await this.removeCachedFile(oldestFile.originalUrl);
        totalSize -= oldestFile.fileSize;
      }
    }

    // Remove expired files
    const now = new Date();
    const expiredFiles = cacheIndex.filter(file => {
      const cachedDate = new Date(file.cachedAt);
      const daysDiff = (now.getTime() - cachedDate.getTime()) / (1000 * 60 * 60 * 24);
      return daysDiff > this.CACHE_EXPIRY_DAYS;
    });

    for (const expiredFile of expiredFiles) {
      await this.removeCachedFile(expiredFile.originalUrl);
    }
  }

  // Get cache statistics
  static async getCacheStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    formattedSize: string;
  }> {
    const cacheIndex = await this.getCacheIndex();
    const totalSize = cacheIndex.reduce((sum, file) => sum + file.fileSize, 0);
    
    return {
      totalFiles: cacheIndex.length,
      totalSize,
      formattedSize: this.formatFileSize(totalSize),
    };
  }

  // Clear all cache
  static async clearAllCache(): Promise<void> {
    try {
      const dirInfo = await FileSystem.getInfoAsync(this.CACHE_DIR);
      if (dirInfo.exists) {
        await FileSystem.deleteAsync(this.CACHE_DIR);
      }
      
      const indexInfo = await FileSystem.getInfoAsync(this.CACHE_INDEX_FILE);
      if (indexInfo.exists) {
        await FileSystem.deleteAsync(this.CACHE_INDEX_FILE);
      }
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  // Helper methods
  private static generateFileId(url: string): string {
    return url.split('/').pop()?.replace(/[^a-zA-Z0-9]/g, '_') || 
           Math.random().toString(36).substring(2, 15);
  }

  private static formatFileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }
}
