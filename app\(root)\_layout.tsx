import { Stack } from "expo-router";

const Layout = () => {
  return (
    <Stack>
      <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
      <Stack.Screen name="(quiz)" options={{ headerShown: false }} />
      <Stack.Screen name="course" options={{ headerShown: false }} />
      <Stack.Screen name="friends" options={{ headerShown: false }} />
      <Stack.Screen name="add-friend" options={{ headerShown: false }} />
      <Stack.Screen name="downloads" options={{ headerShown: false }} />
    </Stack>
  );
};

export default Layout;