// AuthService.ts
import { supabase } from '@/lib/supabase';
import { SignUpData, UserProfile } from '@/types';

export class AuthService {
  // Sign up with OTP email verification
  static async signUp(data: SignUpData) {
    console.log('Attempting to sign up with:', { email: data.email, username: data.username });
    
    const { data: authData, error } = await supabase.auth.signUp({
      email: data.email,
      password: data.password,
      options: {
        emailRedirectTo: undefined, // Don't use email links
        data: {
          username: data.username,
          email: data.email, // Store email in metadata too
        }
      }
    })

    if (error) {
      console.error('Signup error:', error);
      throw error;
    }

    console.log('Signup successful, auth data:', authData);
    return authData;
  }

  // Sign in (only works after email verification)
  static async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) {
      throw error
    }

    return data
  }

  // Sign out
  static async signOut() {
    const { error } = await supabase.auth.signOut()
    if (error) {
      throw error
    }
  }

  // Get user profile
  static async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          // No profile found - this is normal for new users
          console.log('No profile found for user:', userId)
          return null
        }
        console.error('Error fetching profile:', error)
        throw error
      }

      return data
    } catch (error) {
      console.error('getUserProfile error:', error)
      throw error
    }
  }

  // Update user profile
  static async updateProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile> {
    const { data, error } = await supabase
      .from('users')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      throw error
    }

    return data
  }

  // Create initial profile (call this after email verification)
  static async createProfile(userId: string, profileData: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const { data, error } = await supabase
        .from('users')
        .insert({
          id: userId,
          username: profileData.username,
          email: profileData.email,
          display_name: profileData.username, // Default display_name to username
          level: 1,
          coins: 100,
          experience_points: 0,
          total_score: 0,
          games_played: 0,
          games_won: 0,
          current_streak: 0,
          best_streak: 0,
          total_questions_answered: 0,
          correct_answers: 0,
          preferred_categories: [],
          notification_preferences: {
            game_invites: true,
            friend_requests: true,
            achievements: true
          },
          privacy_settings: {
            profile_visible: true,
            stats_visible: true
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          last_active_at: new Date().toISOString(),
          ...profileData // Allow overriding any of the above defaults
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating profile:', error)
        throw error
      }

      return data
    } catch (error) {
      console.error('createProfile error:', error)
      throw error
    }
  }

  // Check if profile exists
  static async profileExists(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id')
        .eq('id', userId)
        .single()

      if (error && error.code !== 'PGRST116') {
        throw error
      }

      return !!data
    } catch (error) {
      console.error('profileExists error:', error)
      return false
    }
  }

  // Verify OTP
  static async verifyOTP(email: string, token: string, type: 'signup' | 'recovery' = 'signup') {
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type
    })

    if (error) {
      throw error
    }

    // Create user profile after successful verification
    if (data.user) {
      try {
        // Check if profile already exists
        const exists = await this.profileExists(data.user.id)
        
        if (!exists) {
          console.log('Creating profile for user:', data.user.id)
          const userEmail = data.user.email || email
          await this.createProfile(data.user.id, {
            username: data.user.user_metadata.username,
            email: userEmail,
            level: 1
          })
          console.log('Profile created successfully')
        } else {
          console.log('Profile already exists for user:', data.user.id)
        }
      } catch (profileError) {
        console.error('Error creating profile:', profileError)
        // Don't throw the error, as the user is still verified
      }
    }

    return data
  }

  // Resend OTP
  static async resendOTP(email: string) {
    const { error } = await supabase.auth.resend({
      type: 'signup',
      email
    })

    if (error) {
      throw error
    }
  }

  private static async verifyBucketAccess(bucketName: string): Promise<boolean> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        console.error('User not authenticated for storage access:', userError);
        return false;
      }
      
      const { data: buckets, error: listError } = await supabase.storage.listBuckets();
      if (listError) {
        console.error('Error listing buckets:', listError);
        return false;
      }
      
      console.log('Available buckets:', buckets?.map(b => ({ name: b.name, id: b.id, public: b.public })));
      
      const targetBucket = buckets?.find(b => b.name === bucketName);
      if (!targetBucket) {
        console.error(`Bucket '${bucketName}' not found in available buckets`);
        return false;
      }
      
      const { data: bucketData, error: bucketError } = await supabase.storage.getBucket(bucketName);
      if (bucketError) {
        console.error(`Error accessing bucket ${bucketName}:`, bucketError);
        return false;
      }
      
      console.log(`Successfully verified access to bucket: ${bucketName}`, bucketData);
      return true;
      
    } catch (error) {
      console.error(`Unexpected error verifying bucket ${bucketName}:`, error);
      return false;
    }
  }
  static async uploadProfilePicture(userId: string, imageUri: string): Promise<string> {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }
      
      if (!imageUri) {
        throw new Error('Image URI is required');
      }
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        throw new Error('User must be authenticated to upload profile picture');
      }
      
      if (user.id !== userId) {
        throw new Error('User can only upload their own profile picture');
      }
      const hasBucketAccess = await this.verifyBucketAccess('avatars');
      if (!hasBucketAccess) {
        throw new Error('Cannot access avatars storage bucket. Please check your Supabase storage configuration and RLS policies.');
      }

      const fileExt = imageUri.split('.').pop()?.toLowerCase() || 'jpg';
      const fileName = `${Date.now()}.${fileExt}`;
      const filePath = `${userId}/${fileName}`;
      const formData = new FormData();
      formData.append('file', {
        uri: imageUri,
        type: `image/${fileExt}`,
        name: fileName,
      } as any);

      const response = await fetch(imageUri);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
      }
      
      const arrayBuffer = await response.arrayBuffer();
      const fileData = new Uint8Array(arrayBuffer);
      
      if (fileData.length === 0) {
        throw new Error('Image file is empty');
      }

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, fileData, {
          contentType: `image/${fileExt}`,
          upsert: true
        });

      if (uploadError) {
        console.error('Upload error details:', uploadError);
        throw new Error(`Failed to upload image: ${uploadError.message}`);
      }

      if (!uploadData) {
        throw new Error('Upload failed: No data returned from storage');
      }

      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(uploadData.path);
      
      if (!publicUrl) {
        throw new Error('Failed to generate public URL for uploaded image');
      }
      await this.updateProfile(userId, {
        avatar_url: publicUrl
      });
      return publicUrl;
      
    } catch (error) {
      console.error('Error in uploadProfilePicture:', error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error('An unexpected error occurred while uploading the profile picture');
      }
    }
  }
}