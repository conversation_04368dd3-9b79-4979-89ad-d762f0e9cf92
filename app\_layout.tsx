import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import 'react-native-reanimated';
import "../global.css";

import { useColorScheme } from '@/hooks/useColorScheme';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    "HelveticaNeueBlack": require("../assets/fonts/HelveticaNeueBlack.otf"),
    "HelveticaNeueBold": require("../assets/fonts/HelveticaNeueBold.otf"),
    "HelveticaNeueHeavy": require("../assets/fonts/HelveticaNeueHeavy.otf"),
    "HelveticaNeueLight": require("../assets/fonts/HelveticaNeueLight.otf"),
    "HelveticaNeueMedium": require("../assets/fonts/HelveticaNeueMedium.otf"),
    "HelveticaNeueRoman": require("../assets/fonts/HelveticaNeueRoman.otf"),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="index" />
      <Stack.Screen name="(root)" />
      <Stack.Screen name="(auth)" />
    </Stack>
  );
}
