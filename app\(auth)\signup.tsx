// SignupPage.tsx
import Input<PERSON>ield from '@/components/InputField'
import { useAuth } from '@/hooks/useAuth'
import { router } from 'expo-router'
import React, { useState } from 'react'
import { Alert, KeyboardAvoidingView, Platform, ScrollView, Text, TouchableOpacity, View } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'

const Signup = () => {
  const { signUp, loading, error } = useAuth()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [username, setUsername] = useState('')
  const [fieldErrors, setFieldErrors] = useState<{[key: string]: string}>({})

  const validateForm = () => {
    const errors: {[key: string]: string} = {}
    
    if (!username.trim()) {
      errors.username = 'Username is required'
    } else if (username.length < 3) {
      errors.username = 'Username must be at least 3 characters'
    }
    
    if (!email.trim()) {
      errors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.email = 'Please enter a valid email address'
    }
    
    if (!password) {
      errors.password = 'Password is required'
    } else if (password.length < 8) {
      errors.password = 'Password must be at least 8 characters'
    }
    
    if (!confirmPassword) {
      errors.confirmPassword = 'Please confirm your password'
    } else if (password !== confirmPassword) {
      errors.confirmPassword = 'Passwords do not match'
    }
    
    setFieldErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }

    try {
      await signUp({
        email: email.trim().toLowerCase(),
        password,
        username: username.trim()
      })
      
      // Navigate to OTP verification screen with email
      router.push({
        pathname: '/(auth)/verify-email',
        params: { email: email.trim().toLowerCase() }
      })
    } catch (err) {
      console.error('Signup error:', err)
      Alert.alert('Error', error || 'Failed to sign up. Please try again.')
    }
  }

  const clearFieldError = (field: string) => {
    if (fieldErrors[field]) {
      setFieldErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  return (
    <SafeAreaView className='flex-1 bg-[#e0e2e4]'>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className='flex-1'
      >
        <ScrollView 
          contentContainerStyle={{ flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <View className='flex-1 p-4 gap-4 justify-center'>
            <View className='mb-8'>
              <Text className='text-3xl font-HelveticaNeueMedium text-[#1d1d1d] text-center mb-2'>
                Create Account
              </Text>
              <Text className='text-[#666] font-HelveticaNeue text-center'>
                Sign up to get started
              </Text>
            </View>

            <View className='gap-4'>
              <View>
                <InputField 
                  image={require('../../assets/images/Profile.png')}
                  placeholder='Username'
                  value={username}
                  onChangeText={(text) => {
                    setUsername(text)
                    clearFieldError('username')
                  }}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                {fieldErrors.username && (
                  <Text className='text-red-500 text-sm mt-1 ml-2'>
                    {fieldErrors.username}
                  </Text>
                )}
              </View>

              <View>
                <InputField 
                  image={require('../../assets/images/Profile.png')}
                  placeholder='Email'
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text)
                    clearFieldError('email')
                  }}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                {fieldErrors.email && (
                  <Text className='text-red-500 text-sm mt-1 ml-2'>
                    {fieldErrors.email}
                  </Text>
                )}
              </View>

              <View>
                <InputField 
                  image={require('../../assets/images/Lock.png')}
                  placeholder='Password'
                  value={password}
                  onChangeText={(text) => {
                    setPassword(text)
                    clearFieldError('password')
                  }}
                  secureTextEntry
                  autoCapitalize="none"
                />
                {fieldErrors.password && (
                  <Text className='text-red-500 text-sm mt-1 ml-2'>
                    {fieldErrors.password}
                  </Text>
                )}
              </View>

              <View>
                <InputField 
                  image={require('../../assets/images/Lock.png')}
                  placeholder='Confirm Password'
                  value={confirmPassword}
                  onChangeText={(text) => {
                    setConfirmPassword(text)
                    clearFieldError('confirmPassword')
                  }}
                  secureTextEntry
                  autoCapitalize="none"
                />
                {fieldErrors.confirmPassword && (
                  <Text className='text-red-500 text-sm mt-1 ml-2'>
                    {fieldErrors.confirmPassword}
                  </Text>
                )}
              </View>
            </View>

            <TouchableOpacity 
              className={`rounded-full mt-4 ${
                loading ? 'bg-gray-400' : 'bg-[#1d1d1d]'
              }`}
              onPress={handleSubmit}
              disabled={loading}
              activeOpacity={0.8}
            >
              <Text className='text-white p-6 font-HelveticaNeueMedium text-center'>
                {loading ? 'Creating Account...' : 'Sign Up'}
              </Text>
            </TouchableOpacity>

            <View className='flex-row justify-center items-center gap-2 mt-4'>
              <Text className='text-[#1d1d1d] font-HelveticaNeueMedium'>
                Already have an account?
              </Text>
              <TouchableOpacity 
                onPress={() => router.push('/(auth)/signin')}
                disabled={loading}
              >
                <Text className='text-[#1d1d1d] font-HelveticaNeueMedium underline'>
                  Sign In
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

export default Signup