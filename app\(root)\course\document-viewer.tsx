import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, Platform, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
// Temporarily disabled to avoid native module issues
// import { FileCacheService } from '../../../services/FileCacheService';

// Conditional import for react-native-file-viewer to handle missing native module
let FileViewer: any = null;
try {
  FileViewer = require('react-native-file-viewer').default;
} catch (error) {
  console.warn('react-native-file-viewer not available:', error);
}

const DocumentViewer = () => {
  const { url, fileName, fileType } = useLocalSearchParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [isOffline, setIsOffline] = useState(false);

  const urlStr = Array.isArray(url) ? url[0] : url;
  const fileNameStr = Array.isArray(fileName) ? fileName[0] : fileName;
  const fileTypeStr = Array.isArray(fileType) ? fileType[0] : fileType;

  useEffect(() => {
    openDocument();
  }, []);

  const openDocument = async () => {
    try {
      setLoading(true);
      setError(null);

      // Temporarily disabled caching functionality
      console.log('Opening document directly...');
      setIsOffline(false);

      // For now, just show the fallback options
      setLoading(false);
      Alert.alert(
        'Document Viewer',
        'Choose how you would like to open this document:',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Open in Browser',
            onPress: () => {
              const googleDocsUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(urlStr || '')}`;
              console.log('Would open:', googleDocsUrl);
              // You can implement Linking.openURL here when ready
            },
          },
        ]
      );
    } catch (err) {
      console.error('Error opening document:', err);
      setError('Failed to open document. Please try again.');
      setLoading(false);
    }
  };

  const openFileViewer = async (filePath: string) => {
    try {
      // Check if FileViewer is available
      if (!FileViewer) {
        // Fallback: Show options to open file externally
        setLoading(false);
        Alert.alert(
          'Document Viewer Not Available',
          'The native document viewer is not available. Would you like to try opening the file in your browser?',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Open in Browser',
              onPress: () => {
                // Use the original URL to open in browser
                const originalUrl = urlStr;
                if (originalUrl) {
                  // For documents, we can try Google Docs viewer
                  const googleDocsUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(originalUrl)}`;
                  Alert.alert(
                    'Open Document',
                    'This will open the document in your browser using Google Docs viewer.',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      {
                        text: 'Open',
                        onPress: () => {
                          // Note: You'll need to import Linking from react-native
                          // Linking.openURL(googleDocsUrl);
                          console.log('Would open:', googleDocsUrl);
                        },
                      },
                    ]
                  );
                }
              },
            },
          ]
        );
        return;
      }

      await FileViewer.open(filePath, {
        showOpenWithDialog: true,
        showAppsSuggestions: true,
        onDismiss: () => {
          console.log('File viewer dismissed');
          setLoading(false);
        },
      });

      setLoading(false);
    } catch (err: any) {
      console.error('FileViewer error:', err);

      if (err.message?.includes('No app associated')) {
        setError('No app found to open this file type. Please install a compatible app.');
      } else if (err.message?.includes('not supported')) {
        setError('This file type is not supported on your device.');
      } else {
        setError('Failed to open document. Please try again.');
      }

      setLoading(false);
    }
  };

  const handleRetry = () => {
    openDocument();
  };

  const handleDownloadForOffline = async () => {
    Alert.alert('Feature Temporarily Disabled', 'Offline download feature is temporarily disabled.');
  };

  const getFileTypeIcon = (type: string) => {
    const lowerType = type.toLowerCase();
    if (lowerType.includes('word') || lowerType.includes('doc')) return 'document-text';
    if (lowerType.includes('powerpoint') || lowerType.includes('ppt')) return 'easel';
    if (lowerType.includes('excel') || lowerType.includes('xls')) return 'grid';
    if (lowerType.includes('pdf')) return 'document';
    return 'document-outline';
  };

  const getFileTypeColor = (type: string) => {
    const lowerType = type.toLowerCase();
    if (lowerType.includes('word') || lowerType.includes('doc')) return '#2B579A';
    if (lowerType.includes('powerpoint') || lowerType.includes('ppt')) return '#D24726';
    if (lowerType.includes('excel') || lowerType.includes('xls')) return '#217346';
    if (lowerType.includes('pdf')) return '#DC2626';
    return '#6B7280';
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center p-4 border-b border-gray-200">
        <TouchableOpacity
          onPress={() => router.back()}
          className="mr-4"
        >
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <View className="flex-1">
          <Text className="text-lg font-semibold" numberOfLines={1}>
            {fileNameStr}
          </Text>
          <View className="flex-row items-center mt-1">
            <Text className="text-sm text-gray-600 mr-2">
              {fileTypeStr?.toUpperCase()}
            </Text>
            {isOffline && (
              <View className="flex-row items-center">
                <Ionicons name="cloud-offline" size={16} color="#059669" />
                <Text className="text-sm text-green-600 ml-1">Offline</Text>
              </View>
            )}
          </View>
        </View>
      </View>

      {/* Content */}
      <View className="flex-1 justify-center items-center p-6">
        {loading ? (
          <View className="items-center">
            <Ionicons 
              name={getFileTypeIcon(fileTypeStr || '')} 
              size={80} 
              color={getFileTypeColor(fileTypeStr || '')} 
            />
            <Text className="text-xl font-semibold mt-4 mb-2">
              {isOffline ? 'Opening Document...' : 'Downloading Document...'}
            </Text>
            {!isOffline && downloadProgress > 0 && (
              <View className="w-64 bg-gray-200 rounded-full h-2 mb-4">
                <View 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${downloadProgress}%` }}
                />
              </View>
            )}
            <Text className="text-gray-600 text-center">
              {isOffline 
                ? 'Opening your document...' 
                : 'Downloading and preparing your document for viewing...'
              }
            </Text>
          </View>
        ) : error ? (
          <View className="items-center">
            <Ionicons name="alert-circle" size={80} color="#EF4444" />
            <Text className="text-xl font-semibold mt-4 mb-2 text-red-600">
              Unable to Open Document
            </Text>
            <Text className="text-gray-600 text-center mb-6">
              {error}
            </Text>
            <View className="space-y-3">
              <TouchableOpacity
                onPress={handleRetry}
                className="bg-blue-500 px-6 py-3 rounded-lg"
              >
                <Text className="text-white font-semibold text-center">Try Again</Text>
              </TouchableOpacity>
              
              {Platform.OS === 'android' && (
                <TouchableOpacity
                  onPress={() => {
                    Alert.alert(
                      'Install Document Viewer',
                      'To view this document, you may need to install Microsoft Office, Google Docs, or another compatible app from the Play Store.',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Open Play Store', onPress: () => {
                          // You could open Play Store here
                        }},
                      ]
                    );
                  }}
                  className="bg-green-500 px-6 py-3 rounded-lg"
                >
                  <Text className="text-white font-semibold text-center">Get Document App</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        ) : (
          <View className="items-center">
            <Ionicons 
              name="checkmark-circle" 
              size={80} 
              color="#059669" 
            />
            <Text className="text-xl font-semibold mt-4 mb-2 text-green-600">
              Document Opened
            </Text>
            <Text className="text-gray-600 text-center mb-6">
              Your document should now be open in the default viewer app.
            </Text>
            
            {!isOffline && (
              <TouchableOpacity
                onPress={handleDownloadForOffline}
                className="bg-blue-500 px-6 py-3 rounded-lg mb-3"
              >
                <Text className="text-white font-semibold text-center">
                  📱 Save for Offline Access
                </Text>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity
              onPress={() => router.back()}
              className="bg-gray-500 px-6 py-3 rounded-lg"
            >
              <Text className="text-white font-semibold text-center">Back to Course</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
};

export default DocumentViewer;
