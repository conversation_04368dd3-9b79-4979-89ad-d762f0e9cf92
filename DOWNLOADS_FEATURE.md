# File Download Feature

## Overview
The app now supports downloading course materials with **dual storage options**: users can choose to save files either to the app's private storage or to their device's Downloads folder. This provides flexibility between convenience and accessibility.

## Features

### 📁 File Organization

#### Option 1: App Storage (Recommended)
Files are saved in the app's private document directory:
```
App Document Directory/
└── quizzy/
    └── courses/
        └── [category_name]/
            └── [section_name]/
                └── [filename].[extension]
```

#### Option 2: Device Downloads
Files are saved to the device's Downloads folder:
```
Device Downloads/
└── [filename].[extension]
```

**Examples:**
- App Storage: `App Document Directory/quizzy/courses/cosc211/slides/lecture1.pdf`
- Device Downloads: `Downloads/lecture1.pdf`

### 🔽 Download Functionality
- **Storage Location Selection**: Modal dialog to choose between app storage and device downloads
- **Download Button**: Each file in course detail screens has a download button
- **Progress Tracking**: Real-time download progress with percentage indicator
- **Status Indicators**: Visual indicators for downloaded files (checkmark icon)
- **Duplicate Handling**: Prompts user when attempting to download existing files
- **Error Handling**: Comprehensive error handling with automatic fallback to app storage
- **Permission Management**: Automatic permission requests with graceful fallbacks

### 📱 Storage Permissions
- **App Storage**: No permissions required (both Android & iOS)
- **Device Downloads**:
  - **Android**: Requires `WRITE_EXTERNAL_STORAGE` and `READ_EXTERNAL_STORAGE` permissions
  - **iOS**: Uses sharing mechanism (no additional permissions needed)
- **Automatic Fallback**: If device downloads permission is denied, automatically falls back to app storage

### 👤 Profile Integration
- **Downloads Summary**: Shows recent downloads and total file count in profile
- **Quick Access**: Direct link to full downloads screen from profile
- **Refresh Support**: Downloads list refreshes when profile is refreshed

### 📋 Downloads Management
- **Full Downloads Screen**: Dedicated screen showing all downloaded files
- **File Organization**: Files grouped by category and section
- **File Information**: Shows file size, download date, and category
- **Delete Functionality**: Remove downloaded files with confirmation
- **File Access**: Files are accessible through the app's interface

## Technical Implementation

### Core Components
1. **FileDownloadManager** (`lib/downloadUtils.ts`): Singleton class handling all download operations
2. **DownloadsSummary** (`components/DownloadsSummary.tsx`): Profile section component
3. **Downloads Screen** (`app/(root)/downloads.tsx`): Full downloads management screen

### Key Dependencies
- `expo-file-system`: File system operations and storage management

### File Types Supported
- **PDF**: `.pdf` files
- **Images**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.webp`, `.bmp`
- **Documents**: `.doc`, `.docx`, `.ppt`, `.pptx`, `.xls`, `.xlsx`
- **Other**: All other file types

## Usage

### For Users
1. Navigate to any course detail screen
2. Find the file you want to download
3. Tap the "Download" button
4. Monitor download progress
5. Access downloaded files from Profile → Downloads

### For Developers
```typescript
import { downloadManager } from '../lib/downloadUtils';

// Download a file
const downloadPath = await downloadManager.downloadFile({
  url: 'https://example.com/file.pdf',
  fileName: 'document.pdf',
  categoryName: 'COSC211',
  sectionName: 'slides',
  onProgress: (progress) => {
    console.log(`Download progress: ${progress.progress * 100}%`);
  }
});

// Check if file is downloaded
const isDownloaded = await downloadManager.isFileDownloaded(
  'COSC211',
  'slides', 
  'document.pdf'
);

// Get all downloaded files for a section
const files = await downloadManager.getDownloadedFiles('COSC211', 'slides');
```

## Configuration

### App Configuration
The app name used in the folder structure is defined in `lib/downloadUtils.ts`:
```typescript
const appName = 'quizzy'; // Change this to match your app name
```

### Storage Configuration
Files are stored in the app's document directory, which doesn't require special permissions. The storage structure is managed automatically by the FileDownloadManager.

## Future Enhancements
- [ ] Bulk download functionality
- [ ] Download queue management
- [ ] Automatic cleanup of old files
- [ ] Download scheduling
- [ ] Offline sync indicators
- [ ] Storage usage analytics
