import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Modal,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { QuizService } from '../services/QuizService';
import { QuizCategory } from '../types';

interface QuizSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectRandom: () => void;
  onSelectCategory: (categoryId: string) => void;
}

const QuizSelectionModal: React.FC<QuizSelectionModalProps> = ({
  visible,
  onClose,
  onSelectRandom,
  onSelectCategory,
}) => {
  const [categories, setCategories] = useState<QuizCategory[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      fetchCategories();
    }
  }, [visible]);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const data = await QuizService.getCategories();
      setCategories(data);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-black/50 justify-center items-center px-4">
        <View className="bg-white rounded-2xl w-full" style={{ maxHeight: '85%' }}>
          {/* Header */}
          <View className="p-6 border-b border-gray-200">
            <View className="flex-row justify-between items-center">
              <Text className="text-xl font-bold text-gray-800">
                Choose Quiz Type
              </Text>
              <TouchableOpacity
                onPress={onClose}
                className="w-8 h-8 rounded-full bg-gray-100 justify-center items-center"
              >
                <Text className="text-gray-600 text-lg">×</Text>
              </TouchableOpacity>
            </View>
            <Text className="text-gray-600 mt-2">
              Select how you want to play your quiz
            </Text>
          </View>

          {/* Content */}
          <ScrollView
            className="px-6 py-4"
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
          >
            {/* Random Questions Option */}
            <TouchableOpacity
              onPress={onSelectRandom}
              className="bg-purple-100 p-4 rounded-xl mb-4 border border-purple-200"
            >
              <View className="flex-row items-center">
                <View className="w-12 h-12 bg-purple-500 rounded-full justify-center items-center mr-4">
                  <Text className="text-white text-xl">🎲</Text>
                </View>
                <View className="flex-1">
                  <Text className="text-lg font-semibold text-gray-800">
                    Random Questions
                  </Text>
                  <Text className="text-gray-600 text-sm">
                    10 random questions from all categories
                  </Text>
                </View>
              </View>
            </TouchableOpacity>

            {/* Categories Section */}
            <Text className="text-lg font-semibold text-gray-800 mb-3">
              Or choose a specific category:
            </Text>

            {loading ? (
              <View className="py-8 justify-center items-center">
                <ActivityIndicator size="large" color="#8B5CF6" />
                <Text className="text-gray-600 mt-2">Loading categories...</Text>
              </View>
            ) : (
              <View>
                {categories.map((category) => (
                  <TouchableOpacity
                    key={category.id}
                    onPress={() => onSelectCategory(category.id)}
                    className="bg-gray-50 p-4 rounded-xl border border-gray-200 mb-3"
                  >
                    <View className="flex-row items-center">
                      <View 
                        className="w-10 h-10 rounded-full justify-center items-center mr-3"
                        style={{ backgroundColor: category.color_hex || '#6B7280' }}
                      >
                        <Text className="text-white text-sm">
                          {category.name.charAt(0).toUpperCase()}
                        </Text>
                      </View>
                      <View className="flex-1">
                        <Text className="text-base font-medium text-gray-800">
                          {category.name}
                        </Text>
                        {category.description && (
                          <Text className="text-gray-600 text-sm mt-1" numberOfLines={2}>
                            {category.description}
                          </Text>
                        )}
                      </View>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

export default QuizSelectionModal;
