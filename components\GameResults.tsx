import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSequence,
  withTiming
} from 'react-native-reanimated';
import { SafeAreaView } from 'react-native-safe-area-context';
import { supabase } from '../lib/supabase';
import { GameService } from '../services/GameService';

interface GameResultsProps {
  gameId: string;
  currentUserId: string;
  totalQuestions: number;
  onClose: () => void;
}

interface PlayerResult {
  user_id: string;
  user: {
    id: string;
    username: string;
    display_name?: string;
    avatar_url?: string;
    level: number;
    experience_points: number;
  };
  current_score: number;
  questions_answered: number;
  correct_answers: number;
  hasFinished: boolean;
}

export const GameResults: React.FC<GameResultsProps> = ({ gameId, currentUserId, totalQuestions, onClose }: any) => {
  const [results, setResults] = useState<PlayerResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [gameFinished, setGameFinished] = useState(false);
  const [currentUser, setCurrentUser] = useState<PlayerResult | null>(null);
  const [opponent, setOpponent] = useState<PlayerResult | null>(null);
  const [winner, setWinner] = useState<'user' | 'opponent' | 'tie' | null>(null);
  const [xpGained, setXpGained] = useState(0);
  const [leveledUp, setLeveledUp] = useState(false);
  const [newLevel, setNewLevel] = useState(0);
  const [refreshing, setRefreshing] = useState(false);
  const [autoRefreshInterval, setAutoRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // Animation values
  const scoreOpacity = useSharedValue(0);
  const xpBarWidth = useSharedValue(0);
  const levelUpScale = useSharedValue(1);
  const resultOpacity = useSharedValue(0);

  useEffect(() => {
    loadGameResults();

    const channel = supabase
      .channel(`game_results_${gameId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'game_participants',
        filter: `game_id=eq.${gameId}`,
      }, () => {
        console.log('Real-time update detected, refreshing results...');
        loadGameResults();
      })
      .subscribe();

    const interval = setInterval(() => {
      if (!gameFinished) {
        console.log('Auto-refreshing game results...');
        loadGameResults();
      }
    }, 3000); 

    setAutoRefreshInterval(interval);

    return () => {
      channel.unsubscribe();
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [gameId, gameFinished]);

  const loadGameResults = async () => {
    try {
      const gameResults = await GameService.getGameResults(gameId);

      const processedResults = gameResults.map((participant: any) => ({
        user_id: participant.user_id,
        user: participant.user,
        current_score: participant.current_score,
        questions_answered: participant.questions_answered,
        correct_answers: participant.correct_answers,
        hasFinished: participant.questions_answered >= totalQuestions,
      }));

      setResults(processedResults);

      const currentUserResult = processedResults.find((p: PlayerResult) => p.user_id === currentUserId);
      const opponentResult = processedResults.find((p: PlayerResult) => p.user_id !== currentUserId);

      setCurrentUser(currentUserResult || null);
      setOpponent(opponentResult || null);

      // Check if both players have finished
      const bothFinished = processedResults.length === 2 &&
                          processedResults.every((p: PlayerResult) => p.hasFinished);

      console.log('Game state:', {
        participants: processedResults.length,
        bothFinished,
        currentUserFinished: currentUserResult?.hasFinished,
        opponentFinished: opponentResult?.hasFinished
      });

      if (bothFinished && !gameFinished) {
        console.log('Both players finished! Starting final calculations...');
        setGameFinished(true);
        determineWinner(currentUserResult, opponentResult);
        await calculateXPAndLevelUp(currentUserResult);
        startAnimations();

        // Clear auto refresh interval since game is finished
        if (autoRefreshInterval) {
          clearInterval(autoRefreshInterval);
          setAutoRefreshInterval(null);
        }
      }

      setLoading(false);
    } catch (error) {
      console.error('Error loading game results:', error);
      setLoading(false);
    }
  };

  const determineWinner = (user: PlayerResult, opp: PlayerResult) => {
    if (!user || !opp) return;

    if (user.current_score > opp.current_score) {
      setWinner('user');
    } else if (opp.current_score > user.current_score) {
      setWinner('opponent');
    } else {
      setWinner('tie');
    }
  };

  const calculateXPAndLevelUp = async (userResult: PlayerResult) => {
    if (!userResult) return;

    // Calculate XP based on performance
    const baseXP = 50; // Base XP for completing a game
    const scoreBonus = Math.floor(userResult.current_score / 10); // 1 XP per 10 points
    const accuracyBonus = userResult.questions_answered > 0 
      ? Math.floor((userResult.correct_answers / userResult.questions_answered) * 30) 
      : 0;
    
    const winBonus = winner === 'user' ? 25 : winner === 'tie' ? 10 : 0;
    
    const totalXP = baseXP + scoreBonus + accuracyBonus + winBonus;
    setXpGained(totalXP);

    // Check for level up
    const currentXP = userResult.user.experience_points;
    const currentLevel = userResult.user.level;
    const newTotalXP = currentXP + totalXP;
    
    // Simple level calculation: 100 XP per level
    const calculatedLevel = Math.floor(newTotalXP / 100) + 1;
    
    if (calculatedLevel > currentLevel) {
      setLeveledUp(true);
      setNewLevel(calculatedLevel);
    }

    // Update user stats in database
    await updateUserStats(userResult.user_id, totalXP, calculatedLevel);
  };

  const updateUserStats = async (userId: string, xpGained: number, newLevel: number) => {
    try {
      // Get current stats first
      const { data: currentUser } = await supabase
        .from('users')
        .select('experience_points, level')
        .eq('id', userId)
        .single();

      if (currentUser) {
        // Only update XP and level here since game stats are handled by GameService.endGame
        await supabase
          .from('users')
          .update({
            experience_points: currentUser.experience_points + xpGained,
            level: newLevel,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);
      }
    } catch (error) {
      console.error('Error updating user XP and level:', error);
    }
  };

  const startAnimations = () => {
    // Animate scores appearing
    scoreOpacity.value = withTiming(1, { duration: 800 });
    
    // Animate XP bar
    setTimeout(() => {
      xpBarWidth.value = withTiming(1, { duration: 1500 });
    }, 1000);

    // Animate level up if applicable
    if (leveledUp) {
      setTimeout(() => {
        levelUpScale.value = withSequence(
          withTiming(1.2, { duration: 300 }),
          withTiming(1, { duration: 300 })
        );
      }, 2000);
    }

    // Show final result
    setTimeout(() => {
      resultOpacity.value = withTiming(1, { duration: 600 });
    }, leveledUp ? 3000 : 2500);
  };

  const handleManualRefresh = async () => {
    setRefreshing(true);
    console.log('Manually refreshing game results...');
    try {
      await loadGameResults();
    } finally {
      setRefreshing(false);
    }
  };

  const handleGoHome = async () => {
    // Clean up auto refresh interval
    if (autoRefreshInterval) {
      clearInterval(autoRefreshInterval);
      setAutoRefreshInterval(null);
    }

    // Clean up the game
    await GameService.endGame(gameId);
    onClose();
    router.push('(tabs)/home');
  };

  const scoreAnimatedStyle = useAnimatedStyle(() => ({
    opacity: scoreOpacity.value,
  }));

  const xpBarAnimatedStyle = useAnimatedStyle(() => ({
    width: `${xpBarWidth.value * 100}%`,
  }));

  const levelUpAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: levelUpScale.value }],
  }));

  const resultAnimatedStyle = useAnimatedStyle(() => ({
    opacity: resultOpacity.value,
  }));

  if (loading) {
    return (
      <SafeAreaView className="flex-1 justify-center items-center bg-white">
        <Text className="text-lg">Loading results...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-blue-50 p-6">
      <Text className="text-2xl font-bold text-center mb-8">Game Results</Text>

      {/* Player vs Opponent */}
      <View className="flex-row justify-between items-center mb-8">
        {/* Current User */}
        <Animated.View style={scoreAnimatedStyle} className="items-center flex-1">
          <View className="w-20 h-20 rounded-full mb-2 bg-blue-200 justify-center items-center">
            {currentUser?.user.avatar_url ? (
              <Image
                source={{ uri: currentUser.user.avatar_url }}
                className="w-20 h-20 rounded-full"
              />
            ) : (
              <Text className="text-2xl font-bold text-blue-600">
                {(currentUser?.user.display_name || currentUser?.user.username || 'You').charAt(0).toUpperCase()}
              </Text>
            )}
          </View>
          <Text className="font-semibold text-lg">
            {currentUser?.user.display_name || currentUser?.user.username || 'You'}
          </Text>
          <Text className="text-2xl font-bold text-blue-600">
            {currentUser?.current_score || 0}
          </Text>
          <Text className="text-sm text-gray-600">
            {currentUser?.correct_answers || 0}/{currentUser?.questions_answered || 0} correct
          </Text>
          {currentUser?.hasFinished && !gameFinished && (
            <Text className="text-xs text-green-600 mt-1">✓ Finished</Text>
          )}
        </Animated.View>

        <Text className="text-3xl font-bold text-gray-400 mx-4">VS</Text>

        {/* Opponent */}
        <Animated.View style={scoreAnimatedStyle} className="items-center flex-1">
          {opponent && opponent.hasFinished ? (
            <>
              <View className="w-20 h-20 rounded-full mb-2 bg-red-200 justify-center items-center">
                {opponent.user.avatar_url ? (
                  <Image
                    source={{ uri: opponent.user.avatar_url }}
                    className="w-20 h-20 rounded-full"
                  />
                ) : (
                  <Text className="text-2xl font-bold text-red-600">
                    {(opponent.user.display_name || opponent.user.username).charAt(0).toUpperCase()}
                  </Text>
                )}
              </View>
              <Text className="font-semibold text-lg">
                {opponent.user.display_name || opponent.user.username}
              </Text>
              <Text className="text-2xl font-bold text-red-600">
                {opponent.current_score}
              </Text>
              <Text className="text-sm text-gray-600">
                {opponent.correct_answers}/{opponent.questions_answered} correct
              </Text>
            </>
          ) : (
            <View className="items-center">
              <View className="w-20 h-20 rounded-full bg-gray-200 mb-2 justify-center items-center">
                <Text className="text-gray-500 text-2xl">?</Text>
              </View>
              <Text className="font-semibold text-lg">Opponent</Text>
              <Text className="text-sm text-orange-500 text-center">
                Waiting for other player...
              </Text>
              <Text className="text-xs text-gray-400 text-center mt-1">
                {loading ? 'Checking...' : 'Auto-refreshing'}
              </Text>
            </View>
          )}
        </Animated.View>
      </View>

      {/* Manual Refresh Button - Show when waiting for opponent */}
      {!gameFinished && (
        <View className="items-center mb-6">
          <TouchableOpacity
            onPress={handleManualRefresh}
            disabled={refreshing}
            className={`px-6 py-3 rounded-lg ${
              refreshing ? 'bg-blue-300' : 'bg-blue-500'
            }`}
          >
            <Text className="text-white font-semibold">
              {refreshing ? '🔄 Refreshing...' : '🔄 Refresh Results'}
            </Text>
          </TouchableOpacity>
          <Text className="text-sm text-gray-500 mt-2 text-center">
            Auto-refreshing every 3 seconds
          </Text>
        </View>
      )}

      {gameFinished && (
        <>
          {/* XP Gained Section */}
          <View className="bg-white rounded-lg p-4 mb-6 shadow-sm">
            <Text className="text-lg font-semibold mb-3">Experience Gained</Text>
            <Text className="text-3xl font-bold text-green-600 text-center mb-2">
              +{xpGained} XP
            </Text>
            
            {/* XP Progress Bar */}
            <View className="bg-gray-200 rounded-full h-3 mb-2">
              <Animated.View 
                style={xpBarAnimatedStyle}
                className="bg-green-500 rounded-full h-3"
              />
            </View>
            
            {leveledUp && (
              <Animated.View style={levelUpAnimatedStyle} className="items-center mt-3">
                <Text className="text-xl font-bold text-yellow-500">LEVEL UP!</Text>
                <Text className="text-lg">Level {newLevel}</Text>
              </Animated.View>
            )}
          </View>

          {/* Winner Announcement */}
          <Animated.View style={resultAnimatedStyle} className="items-center mb-8">
            {winner === 'user' && (
              <Text className="text-3xl font-bold text-green-600">🎉 YOU WIN! 🎉</Text>
            )}
            {winner === 'opponent' && (
              <Text className="text-3xl font-bold text-red-600">😔 YOU LOSE 😔</Text>
            )}
            {winner === 'tie' && (
              <Text className="text-3xl font-bold text-yellow-600">🤝 IT'S A TIE! 🤝</Text>
            )}
          </Animated.View>

          {/* Return Home Button */}
          <Animated.View style={resultAnimatedStyle}>
            <TouchableOpacity
              onPress={handleGoHome}
              className="bg-blue-500 py-4 px-8 rounded-lg"
            >
              <Text className="text-white text-lg font-semibold text-center">
                Return Home
              </Text>
            </TouchableOpacity>
          </Animated.View>
        </>
      )}
    </SafeAreaView>
  );
};
