import { Stack } from "expo-router";

const Layout = () => {
  return (
    <Stack>
      <Stack.Screen 
        name="[categoryId]" 
        options={{ 
          headerShown: false,
          title: "Course Materials"
        }} 
      />
      <Stack.Screen 
        name="pdf-viewer" 
        options={{ 
          headerShown: false,
          title: "PDF Viewer",
          presentation: "modal"
        }} 
      />
      <Stack.Screen
        name="image-viewer"
        options={{
          headerShown: false,
          title: "Image Viewer",
          presentation: "modal"
        }}
      />
      <Stack.Screen
        name="document-viewer"
        options={{
          headerShown: false,
          title: "Document Viewer",
          presentation: "modal"
        }}
      />
      <Stack.Screen
        name="simple-document-viewer"
        options={{
          headerShown: false,
          title: "Document Viewer",
          presentation: "modal"
        }}
      />
    </Stack>
  );
};

export default Layout;
