import FriendsSection from '@/components/FriendsSection';
import { useAuth } from '@/hooks/useAuth';
import { AuthService } from '@/services/AuthService';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  Alert,
  Dimensions,
  Image,
  Modal,
  RefreshControl,
  ScrollView,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

const { width } = Dimensions.get('window');

const Profile = () => {
  const { profile, user, signOut, refreshProfile, updateProfile } = useAuth();
  const [uploading, setUploading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editField, setEditField] = useState<string>('');
  const [editValue, setEditValue] = useState<string>('');

  useEffect(() => {
    if (user && !profile) {
      refreshProfile();
    }
  }, [user]);

  const handleLogout = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await signOut();
              router.replace('/(auth)/signin');
            } catch (error) {
              console.error('Logout error:', error);
            }
          }
        }
      ]
    );
  };

  const handleProfilePicturePress = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera roll permissions to change your profile picture.');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0].uri) {
      setUploading(true);
      try {
        if (!user) throw new Error('No user found');
        await AuthService.uploadProfilePicture(user.id, result.assets[0].uri);
        await refreshProfile();
        Alert.alert('Success', 'Profile picture updated successfully');
      } catch (error) {
        console.error('Error uploading profile picture:', error);
        Alert.alert('Error', 'Failed to update profile picture. Please try again.');
      } finally {
        setUploading(false);
      }
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await refreshProfile();
    setRefreshing(false);
  };

  const handleEditField = (field: string, currentValue: string) => {
    setEditField(field);
    setEditValue(currentValue);
    setEditModalVisible(true);
  };

  const saveEdit = async () => {
    if (!editField || !profile) return;

    try {
      await updateProfile({ [editField]: editValue });
      setEditModalVisible(false);
      Alert.alert('Success', 'Profile updated successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to update profile');
    }
  };

  const getAccuracyPercentage = () => {
    if (!profile || profile.total_questions_answered === 0) return 0;
    return Math.round((profile.correct_answers / profile.total_questions_answered) * 100);
  };

  const getWinRate = () => {
    if (!profile || profile.games_played === 0) return 0;
    return Math.round((profile.games_won / profile.games_played) * 100);
  };

  const getLevelProgress = () => {
    if (!profile) return 0;
    const currentLevelXP = (profile.level - 1) * 100;
    const nextLevelXP = profile.level * 100;
    const progress = ((profile.experience_points - currentLevelXP) / (nextLevelXP - currentLevelXP)) * 100;
    return Math.max(0, Math.min(100, progress));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (!profile) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50 justify-center items-center">
        <Text className="text-gray-500">Loading profile...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        showsVerticalScrollIndicator={false}
      >
        {/* Header with Profile Picture */}
        <View className="bg-red-400 bg-gradient-to-br from-blue-500 to-purple-600 px-6 pt-6 pb-8">
          <View className="flex-row justify-between items-start mb-6">
            <View className="flex-1">
              <Text className="text-white text-2xl font-bold mb-1">
                {profile.display_name || profile.username}
              </Text>
              <Text className="text-blue-100 text-base">
                @{profile.username}
              </Text>
              {profile.bio && (
                <Text className="text-blue-100 text-sm mt-2 leading-5">
                  {profile.bio}
                </Text>
              )}
            </View>

            <View className="items-center">
              <TouchableOpacity
                onPress={handleProfilePicturePress}
                disabled={uploading}
                className="relative"
              >
                <View className="w-24 h-24 rounded-full bg-white/20 border-4 border-white/30 justify-center items-center">
                  {profile.avatar_url ? (
                    <Image
                      source={{ uri: profile.avatar_url }}
                      className="w-20 h-20 rounded-full"
                      resizeMode="cover"
                    />
                  ) : (
                    <Ionicons name="person" size={40} color="white" />
                  )}
                </View>
                <View className="absolute -bottom-1 -right-1 w-8 h-8 bg-blue-500 rounded-full justify-center items-center border-2 border-white">
                  <Ionicons name="camera" size={16} color="white" />
                </View>
              </TouchableOpacity>

              {uploading && (
                <Text className="text-white text-xs mt-2">Uploading...</Text>
              )}
            </View>
          </View>

          {/* Level Progress */}
          <View className="bg-white/20 rounded-2xl p-4 backdrop-blur-sm">
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-white font-semibold">Level {profile.level}</Text>
              <Text className="text-blue-100 text-sm">{profile.experience_points} XP</Text>
            </View>
            <View className="bg-white/30 rounded-full h-3">
              <View
                className="bg-white rounded-full h-3"
                style={{ width: `${getLevelProgress()}%` }}
              />
            </View>
            <Text className="text-blue-100 text-xs mt-1 text-center">
              {100 - (profile.experience_points % 100)} XP to next level
            </Text>
          </View>
        </View>

        {/* Stats Grid */}
        <View className="px-6 py-4">
          <Text className="text-xl font-bold text-gray-900 mb-4">📊 Game Statistics</Text>

          <View className="flex-row flex-wrap justify-between">
            {/* Total Score */}
            <View className="bg-white rounded-2xl p-4 shadow-sm mb-4" style={{ width: (width - 48 - 8) / 2 }}>
              <View className="flex-row items-center mb-2">
                <View className="w-10 h-10 bg-blue-100 rounded-full justify-center items-center mr-3">
                  <Ionicons name="trophy" size={20} color="#3B82F6" />
                </View>
                <Text className="text-gray-600 text-sm font-medium">Total Score</Text>
              </View>
              <Text className="text-2xl font-bold text-gray-900">{profile.total_score.toLocaleString()}</Text>
            </View>

            {/* Games Played */}
            <View className="bg-white rounded-2xl p-4 shadow-sm mb-4" style={{ width: (width - 48 - 8) / 2 }}>
              <View className="flex-row items-center mb-2">
                <View className="w-10 h-10 bg-green-100 rounded-full justify-center items-center mr-3">
                  <Ionicons name="game-controller" size={20} color="#10B981" />
                </View>
                <Text className="text-gray-600 text-sm font-medium">Games Played</Text>
              </View>
              <Text className="text-2xl font-bold text-gray-900">{profile.games_played}</Text>
            </View>

            {/* Games Won */}
            <View className="bg-white rounded-2xl p-4 shadow-sm mb-4" style={{ width: (width - 48 - 8) / 2 }}>
              <View className="flex-row items-center mb-2">
                <View className="w-10 h-10 bg-yellow-100 rounded-full justify-center items-center mr-3">
                  <Ionicons name="medal" size={20} color="#F59E0B" />
                </View>
                <Text className="text-gray-600 text-sm font-medium">Games Won</Text>
              </View>
              <Text className="text-2xl font-bold text-gray-900">{profile.games_won}</Text>
              <Text className="text-sm text-gray-500">{getWinRate()}% win rate</Text>
            </View>

            {/* Current Streak */}
            <View className="bg-white rounded-2xl p-4 shadow-sm mb-4" style={{ width: (width - 48 - 8) / 2 }}>
              <View className="flex-row items-center mb-2">
                <View className="w-10 h-10 bg-orange-100 rounded-full justify-center items-center mr-3">
                  <Ionicons name="flame" size={20} color="#F97316" />
                </View>
                <Text className="text-gray-600 text-sm font-medium">Current Streak</Text>
              </View>
              <Text className="text-2xl font-bold text-gray-900">{profile.current_streak}</Text>
              <Text className="text-sm text-gray-500">Best: {profile.best_streak}</Text>
            </View>
          </View>
        </View>

        {/* Performance Metrics */}
        <View className="px-6 py-4">
          <Text className="text-xl font-bold text-gray-900 mb-4">🎯 Performance</Text>

          <View className="bg-white rounded-2xl p-4 shadow-sm mb-4">
            <View className="flex-row justify-between items-center mb-3">
              <Text className="text-gray-700 font-medium">Questions Answered</Text>
              <Text className="text-lg font-bold text-gray-900">{profile.total_questions_answered}</Text>
            </View>
            <View className="flex-row justify-between items-center mb-3">
              <Text className="text-gray-700 font-medium">Correct Answers</Text>
              <Text className="text-lg font-bold text-green-600">{profile.correct_answers}</Text>
            </View>
            <View className="flex-row justify-between items-center">
              <Text className="text-gray-700 font-medium">Accuracy Rate</Text>
              <Text className="text-lg font-bold text-blue-600">{getAccuracyPercentage()}%</Text>
            </View>
          </View>

          <View className="bg-white rounded-2xl p-4 shadow-sm">
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-gray-700 font-medium">Coins</Text>
              <View className="flex-row items-center">
                <Ionicons name="diamond" size={16} color="#F59E0B" />
                <Text className="text-lg font-bold text-yellow-600 ml-1">{profile.coins}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Account Information */}
        <View className="px-6 py-4">
          <Text className="text-xl font-bold text-gray-900 mb-4">👤 Account Information</Text>

          <View className="bg-white rounded-2xl p-4 shadow-sm space-y-4">
            <TouchableOpacity
              className="flex-row justify-between items-center py-2"
              onPress={() => handleEditField('display_name', profile.display_name || '')}
            >
              <Text className="text-gray-700 font-medium">Display Name</Text>
              <View className="flex-row items-center">
                <Text className="text-gray-900 mr-2">{profile.display_name || 'Not set'}</Text>
                <Ionicons name="chevron-forward" size={16} color="#9CA3AF" />
              </View>
            </TouchableOpacity>

            <View className="border-t border-gray-100" />

            <TouchableOpacity
              className="flex-row justify-between items-center py-2"
              onPress={() => handleEditField('bio', profile.bio || '')}
            >
              <Text className="text-gray-700 font-medium">Bio</Text>
              <View className="flex-row items-center">
                <Text className="text-gray-900 mr-2" numberOfLines={1}>
                  {profile.bio || 'Add a bio'}
                </Text>
                <Ionicons name="chevron-forward" size={16} color="#9CA3AF" />
              </View>
            </TouchableOpacity>

            <View className="border-t border-gray-100" />

            <View className="flex-row justify-between items-center py-2">
              <Text className="text-gray-700 font-medium">Email</Text>
              <Text className="text-gray-900">{profile.email}</Text>
            </View>

            <View className="border-t border-gray-100" />

            <TouchableOpacity
              className="flex-row justify-between items-center py-2"
              onPress={() => handleEditField('country', profile.country || '')}
            >
              <Text className="text-gray-700 font-medium">Country</Text>
              <View className="flex-row items-center">
                <Text className="text-gray-900 mr-2">{profile.country || 'Not set'}</Text>
                <Ionicons name="chevron-forward" size={16} color="#9CA3AF" />
              </View>
            </TouchableOpacity>

            <View className="border-t border-gray-100" />

            <View className="flex-row justify-between items-center py-2">
              <Text className="text-gray-700 font-medium">Member Since</Text>
              <Text className="text-gray-900">{formatDate(profile.created_at)}</Text>
            </View>

            <View className="border-t border-gray-100" />

            <View className="flex-row justify-between items-center py-2">
              <Text className="text-gray-700 font-medium">Last Active</Text>
              <Text className="text-gray-900">{formatDate(profile.last_active_at)}</Text>
            </View>
          </View>
        </View>

        {/* Friends Section */}
        <FriendsSection />

        {/* Settings */}
        <View className="px-6 py-4">
          <Text className="text-xl font-bold text-gray-900 mb-4">⚙️ Settings</Text>

          <View className="bg-white rounded-2xl p-4 shadow-sm space-y-4">
            <View className="flex-row justify-between items-center py-2">
              <Text className="text-gray-700 font-medium">Profile Visible</Text>
              <Switch
                value={profile.privacy_settings?.profile_visible ?? true}
                onValueChange={(value) => updateProfile({
                  privacy_settings: {
                    ...profile.privacy_settings,
                    profile_visible: value
                  }
                })}
              />
            </View>

            <View className="border-t border-gray-100" />

            <View className="flex-row justify-between items-center py-2">
              <Text className="text-gray-700 font-medium">Stats Visible</Text>
              <Switch
                value={profile.privacy_settings?.stats_visible ?? true}
                onValueChange={(value) => updateProfile({
                  privacy_settings: {
                    ...profile.privacy_settings,
                    stats_visible: value
                  }
                })}
              />
            </View>

            <View className="border-t border-gray-100" />

            <View className="flex-row justify-between items-center py-2">
              <Text className="text-gray-700 font-medium">Game Invites</Text>
              <Switch
                value={profile.notification_preferences?.game_invites ?? true}
                onValueChange={(value) => updateProfile({
                  notification_preferences: {
                    ...profile.notification_preferences,
                    game_invites: value
                  }
                })}
              />
            </View>

            <View className="border-t border-gray-100" />

            <View className="flex-row justify-between items-center py-2">
              <Text className="text-gray-700 font-medium">Friend Requests</Text>
              <Switch
                value={profile.notification_preferences?.friend_requests ?? true}
                onValueChange={(value) => updateProfile({
                  notification_preferences: {
                    ...profile.notification_preferences,
                    friend_requests: value
                  }
                })}
              />
            </View>

            <View className="border-t border-gray-100" />

            <View className="flex-row justify-between items-center py-2">
              <Text className="text-gray-700 font-medium">Achievement Notifications</Text>
              <Switch
                value={profile.notification_preferences?.achievements ?? true}
                onValueChange={(value) => updateProfile({
                  notification_preferences: {
                    ...profile.notification_preferences,
                    achievements: value
                  }
                })}
              />
            </View>
          </View>
        </View>

        {/* Sign Out Button */}
        <View className="px-6 py-4 mb-28">
          <TouchableOpacity
            onPress={handleLogout}
            className="bg-red-500 rounded-2xl p-4 flex-row justify-center items-center"
          >
            <Ionicons name="log-out-outline" size={20} color="white" />
            <Text className="text-white font-semibold ml-2">Sign Out</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Edit Modal */}
      <Modal
        visible={editModalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setEditModalVisible(false)}
      >
        <View className="flex-1 bg-black/50 justify-end">
          <View className="bg-white rounded-t-3xl p-6">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-lg font-bold text-gray-900">
                Edit {editField.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </Text>
              <TouchableOpacity onPress={() => setEditModalVisible(false)}>
                <Ionicons name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <TextInput
              value={editValue}
              onChangeText={setEditValue}
              placeholder={`Enter ${editField.replace('_', ' ')}`}
              className="border border-gray-300 rounded-xl p-4 mb-4 text-base"
              multiline={editField === 'bio'}
              numberOfLines={editField === 'bio' ? 3 : 1}
            />

            <View className="flex-row space-x-3">
              <TouchableOpacity
                onPress={() => setEditModalVisible(false)}
                className="flex-1 bg-gray-200 rounded-xl p-4"
              >
                <Text className="text-gray-700 font-semibold text-center">Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={saveEdit}
                className="flex-1 bg-blue-500 rounded-xl p-4"
              >
                <Text className="text-white font-semibold text-center">Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default Profile;