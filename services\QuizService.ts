import { supabase } from '../lib/supabase';

interface Quiz {
  id: string;
  title: string;
  description: string;
  category_id: string;
  difficulty: 'easy' | 'medium' | 'hard';
  total_questions: number;
  estimated_duration: number;
  is_published: boolean;
  is_featured: boolean;
  times_played: number;
  average_score: number;
}

interface Question {
  id: string;
  quiz_id: string;
  question_text: string;
  question_type: 'multiple_choice' | 'true_false' | 'text_input';
  options?: string[];
  correct_answer: string;
  base_points: number;
  time_limit: number;
  difficulty: 'easy' | 'medium' | 'hard';
  question_order: number;
}

interface QuizCategory {
  id: string;
  name: string;
  description: string;
  icon_url?: string;
  color_hex?: string;
}

export class QuizService {
  static async getCategories(): Promise<QuizCategory[]> {
    const { data, error } = await supabase
      .from('quiz_categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order', { ascending: true });

    if (error) throw error;
    return data || [];
  }

  static async getQuizzesByCategory(categoryId: string, limit = 20): Promise<Quiz[]> {
    const { data, error } = await supabase
      .from('quizzes')
      .select('*')
      .eq('category_id', categoryId)
      .eq('is_published', true)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  static async getFeaturedQuizzes(limit = 10): Promise<Quiz[]> {
    const { data, error } = await supabase
      .from('quizzes')
      .select('*')
      .eq('is_featured', true)
      .eq('is_published', true)
      .order('times_played', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  static async getQuizById(quizId: string): Promise<Quiz | null> {
    const { data, error } = await supabase
      .from('quizzes')
      .select('*')
      .eq('id', quizId)
      .single();

    if (error) throw error;
    return data;
  }



  static async searchQuizzes(query: string, categoryId?: string): Promise<Quiz[]> {
    let queryBuilder = supabase
      .from('quizzes')
      .select('*')
      .eq('is_published', true)
      .ilike('title', `%${query}%`);

    if (categoryId) {
      queryBuilder = queryBuilder.eq('category_id', categoryId);
    }

    const { data, error } = await queryBuilder
      .order('times_played', { ascending: false })
      .limit(50);

    if (error) throw error;
    return data || [];
  }

  // Get random questions from all quizzes
  static async getRandomQuestions(limit = 10): Promise<Question[]> {
    // First get all questions from published quizzes
    const { data: allQuestions, error } = await supabase
      .from('questions')
      .select(`
        *,
        quiz:quizzes!inner(is_published)
      `)
      .eq('quiz.is_published', true);

    if (error) throw error;

    // Shuffle and take the required number
    const shuffled = (allQuestions || []).sort(() => Math.random() - 0.5);
    return shuffled.slice(0, limit);
  }

  // Get random questions from a specific category
  static async getRandomQuestionsByCategory(categoryId: string, limit = 10): Promise<Question[]> {
    // First get all questions from published quizzes in the category
    const { data: allQuestions, error } = await supabase
      .from('questions')
      .select(`
        *,
        quiz:quizzes!inner(category_id, is_published)
      `)
      .eq('quiz.category_id', categoryId)
      .eq('quiz.is_published', true);

    if (error) throw error;

    // Shuffle and take the required number
    const shuffled = (allQuestions || []).sort(() => Math.random() - 0.5);
    return shuffled.slice(0, limit);
  }

  // Store random questions in memory/cache for a session
  private static randomQuestionSessions: Map<string, Question[]> = new Map();

  // Create standardized session IDs for matchmaking
  static createRandomQuizSessionId(categoryId?: string): string {
    if (categoryId) {
      return `random_category_${categoryId}`;
    }
    return 'random_all_categories';
  }

  // Store questions for a session (used by game service)
  static storeQuestionsForSession(sessionId: string, questions: Question[]): void {
    this.randomQuestionSessions.set(sessionId, questions);

    // Clean up after 2 hours
    setTimeout(() => {
      this.randomQuestionSessions.delete(sessionId);
    }, 2 * 60 * 60 * 1000);
  }

  // Get questions for a random session
  static getRandomSessionQuestions(sessionId: string): Question[] {
    return this.randomQuestionSessions.get(sessionId) || [];
  }

  // Check if a quiz ID is a random session
  static isRandomSession(quizId: string): boolean {
    return quizId.startsWith('random_');
  }

  // Enhanced getQuizQuestions to handle random sessions
  static async getQuizQuestions(quizId: string): Promise<Question[]> {
    // Check if this is a random session
    if (this.isRandomSession(quizId)) {
      return this.getRandomSessionQuestions(quizId);
    }

    // Regular quiz questions
    const { data, error } = await supabase
      .from('questions')
      .select('*')
      .eq('quiz_id', quizId)
      .order('question_order', { ascending: true });

    if (error) throw error;
    return data || [];
  }
}