import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { FileCacheService } from '../services/FileCacheService';

interface CachedFile {
  id: string;
  fileName: string;
  filePath: string;
  originalUrl: string;
  fileType: string;
  fileSize: number;
  cachedAt: string;
  lastAccessed: string;
}

interface CacheStats {
  totalFiles: number;
  totalSize: number;
  formattedSize: string;
}

const CacheManager: React.FC = () => {
  const [cachedFiles, setCachedFiles] = useState<CachedFile[]>([]);
  const [cacheStats, setCacheStats] = useState<CacheStats>({ totalFiles: 0, totalSize: 0, formattedSize: '0 Bytes' });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCacheData();
  }, []);

  const loadCacheData = async () => {
    try {
      setLoading(true);
      const [files, stats] = await Promise.all([
        FileCacheService.getCacheIndex(),
        FileCacheService.getCacheStats(),
      ]);
      
      setCachedFiles(files);
      setCacheStats(stats);
    } catch (error) {
      console.error('Error loading cache data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteFile = async (fileUrl: string, fileName: string) => {
    Alert.alert(
      'Delete Cached File',
      `Are you sure you want to delete "${fileName}" from offline storage?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await FileCacheService.removeCachedFile(fileUrl);
              await loadCacheData();
            } catch (error) {
              console.error('Error deleting cached file:', error);
              Alert.alert('Error', 'Failed to delete cached file');
            }
          },
        },
      ]
    );
  };

  const handleClearAllCache = () => {
    Alert.alert(
      'Clear All Cache',
      'Are you sure you want to delete all offline files? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: async () => {
            try {
              await FileCacheService.clearAllCache();
              await loadCacheData();
              Alert.alert('Success', 'All cached files have been deleted');
            } catch (error) {
              console.error('Error clearing cache:', error);
              Alert.alert('Error', 'Failed to clear cache');
            }
          },
        },
      ]
    );
  };

  const getFileIcon = (fileType: string, fileName: string) => {
    const ext = fileName.toLowerCase().split('.').pop();
    
    if (fileType === 'pdf' || ext === 'pdf') return 'document-text';
    if (fileType === 'image' || ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(ext || '')) return 'image';
    if (['ppt', 'pptx'].includes(ext || '')) return 'easel';
    if (['xls', 'xlsx'].includes(ext || '')) return 'grid';
    if (['doc', 'docx'].includes(ext || '')) return 'document-text';
    return 'document';
  };

  const getFileIconColor = (fileType: string, fileName: string) => {
    const ext = fileName.toLowerCase().split('.').pop();
    
    if (fileType === 'pdf' || ext === 'pdf') return '#DC2626';
    if (fileType === 'image' || ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(ext || '')) return '#059669';
    if (['ppt', 'pptx'].includes(ext || '')) return '#D24726';
    if (['xls', 'xlsx'].includes(ext || '')) return '#217346';
    if (['doc', 'docx'].includes(ext || '')) return '#2B579A';
    return '#6B7280';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center p-4">
        <Text className="text-lg">Loading cache data...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-white">
      {/* Header Stats */}
      <View className="bg-blue-50 p-4 border-b border-gray-200">
        <Text className="text-lg font-semibold mb-2">Offline Storage</Text>
        <View className="flex-row justify-between">
          <View>
            <Text className="text-sm text-gray-600">Files Cached</Text>
            <Text className="text-xl font-bold text-blue-600">{cacheStats.totalFiles}</Text>
          </View>
          <View>
            <Text className="text-sm text-gray-600">Storage Used</Text>
            <Text className="text-xl font-bold text-blue-600">{cacheStats.formattedSize}</Text>
          </View>
        </View>
      </View>

      {/* Action Buttons */}
      <View className="p-4 border-b border-gray-200">
        <TouchableOpacity
          onPress={handleClearAllCache}
          disabled={cachedFiles.length === 0}
          className={`py-3 px-4 rounded-lg ${
            cachedFiles.length === 0 ? 'bg-gray-200' : 'bg-red-500'
          }`}
        >
          <Text className={`text-center font-semibold ${
            cachedFiles.length === 0 ? 'text-gray-500' : 'text-white'
          }`}>
            Clear All Cache
          </Text>
        </TouchableOpacity>
      </View>

      {/* Cached Files List */}
      <ScrollView className="flex-1">
        {cachedFiles.length === 0 ? (
          <View className="flex-1 justify-center items-center p-8">
            <Ionicons name="cloud-offline-outline" size={64} color="#9CA3AF" />
            <Text className="text-gray-500 text-center mt-4 text-lg">
              No Offline Files
            </Text>
            <Text className="text-gray-400 text-center mt-2">
              Download files for offline access from course materials
            </Text>
          </View>
        ) : (
          <View className="p-4">
            {cachedFiles.map((file) => (
              <View key={file.id} className="bg-gray-50 rounded-lg p-3 mb-3">
                <View className="flex-row items-center">
                  <Ionicons
                    name={getFileIcon(file.fileType, file.fileName) as any}
                    size={24}
                    color={getFileIconColor(file.fileType, file.fileName)}
                    className="mr-3"
                  />
                  <View className="flex-1">
                    <Text className="font-medium text-gray-900" numberOfLines={1}>
                      {file.fileName}
                    </Text>
                    <Text className="text-sm text-gray-500">
                      {formatFileSize(file.fileSize)} • Cached {formatDate(file.cachedAt)}
                    </Text>
                    <Text className="text-xs text-gray-400">
                      Last accessed {formatDate(file.lastAccessed)}
                    </Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => handleDeleteFile(file.originalUrl, file.fileName)}
                    className="p-2"
                  >
                    <Ionicons name="trash-outline" size={20} color="#EF4444" />
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

export default CacheManager;
