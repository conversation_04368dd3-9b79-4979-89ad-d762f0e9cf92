import { router } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, Alert, Text, TouchableOpacity } from 'react-native';
import { QuizService } from '../services/QuizService';
import QuizSelectionModal from './QuizSelectionModal';

const Play = () => {
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);

  const handlePressPlay = () => {
    setShowModal(true);
  };

  const handleSelectRandom = async () => {
    try {
      setLoading(true);
      setShowModal(false);

      // Create a standardized session ID for random questions
      const quizId = QuizService.createRandomQuizSessionId();
      console.log('🎲 Random quiz session ID:', quizId);

      // Navigate to the game screen - questions will be generated when game starts
      router.push({
        pathname: '(quiz)/duoGameScreen',
        params: {
          quizId,
          isRandomQuiz: 'true'
        }
      });
    } catch (error) {
      console.error('Error starting random quiz:', error);
      Alert.alert('Error', 'Failed to start random quiz. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectCategory = async (categoryId: string) => {
    try {
      setLoading(true);
      setShowModal(false);

      // Create a standardized session ID for this category
      const quizId = QuizService.createRandomQuizSessionId(categoryId);
      console.log('📂 Category quiz session ID:', quizId, 'for category:', categoryId);

      // Navigate to the game screen - questions will be generated when game starts
      router.push({
        pathname: '(quiz)/duoGameScreen',
        params: {
          quizId,
          isRandomQuiz: 'true',
          categoryId
        }
      });
    } catch (error) {
      console.error('Error starting category quiz:', error);
      Alert.alert('Error', 'Failed to start category quiz. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <TouchableOpacity
        className='absolute bottom-[120px] right-[18px] p-6 rounded-full bg-[#1d1d1d]'
        onPress={handlePressPlay}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator size="small" color="white" />
        ) : (
          <Text className='text-[24px]'>⚔️</Text>
        )}
      </TouchableOpacity>

      <QuizSelectionModal
        visible={showModal}
        onClose={() => setShowModal(false)}
        onSelectRandom={handleSelectRandom}
        onSelectCategory={handleSelectCategory}
      />
    </>
  );
}

export default Play