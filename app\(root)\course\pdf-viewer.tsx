import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';

const PDFViewer = () => {
  const { url, fileName } = useLocalSearchParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const urlStr = Array.isArray(url) ? url[0] : url;
  const fileNameStr = Array.isArray(fileName) ? fileName[0] : fileName;

  // Use Google Docs Viewer for PDF display
  const pdfViewerUrl = `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(urlStr)}`;

  const handleLoadEnd = () => {
    setLoading(false);
  };

  const handleError = () => {
    setLoading(false);
    setError('Failed to load PDF');
    console.error('PDF Error: Failed to load document');
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center p-4 border-b border-gray-200 bg-white">
        <TouchableOpacity
          onPress={() => router.back()}
          className="mr-4"
        >
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <View className="flex-1">
          <Text className="text-lg font-semibold" numberOfLines={1}>
            {fileNameStr}
          </Text>
        </View>
      </View>

      {/* PDF Viewer */}
      <View className="flex-1">
        {loading && (
          <View className="absolute inset-0 justify-center items-center bg-white z-10">
            <ActivityIndicator size="large" color="#0000ff" />
            <Text className="text-lg mt-2">Loading PDF...</Text>
          </View>
        )}

        {error ? (
          <View className="flex-1 justify-center items-center p-4">
            <Ionicons name="document-text-outline" size={64} color="#EF4444" />
            <Text className="text-red-500 text-center mt-4 text-lg">
              {error}
            </Text>
            <TouchableOpacity
              onPress={() => router.back()}
              className="bg-blue-500 px-6 py-3 rounded-lg mt-4"
            >
              <Text className="text-white font-semibold">Go Back</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <WebView
            source={{ uri: pdfViewerUrl }}
            onLoadEnd={handleLoadEnd}
            onError={handleError}
            style={{ flex: 1 }}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            startInLoadingState={true}
            scalesPageToFit={true}
            renderLoading={() => (
              <View className="flex-1 justify-center items-center">
                <ActivityIndicator size="large" color="#0000ff" />
                <Text className="mt-2">Loading PDF...</Text>
              </View>
            )}
          />
        )}
      </View>


    </SafeAreaView>
  );
};

export default PDFViewer;
