import { supabase } from '../lib/supabase';
import { Friendship, FriendRequest, UserProfile } from '../types';

export class FriendService {
  // Send a friend request
  static async sendFriendRequest(requesterId: string, addresseeId: string): Promise<Friendship> {
    // Check if friendship already exists
    const { data: existingFriendship } = await supabase
      .from('friendships')
      .select('*')
      .or(`and(requester_id.eq.${requesterId},addressee_id.eq.${addresseeId}),and(requester_id.eq.${addresseeId},addressee_id.eq.${requesterId})`)
      .single();

    if (existingFriendship) {
      throw new Error('Friendship already exists or request already sent');
    }

    const { data, error } = await supabase
      .from('friendships')
      .insert({
        requester_id: requesterId,
        addressee_id: addresseeId,
        status: 'pending'
      })
      .select(`
        *,
        requester:users!requester_id(*),
        addressee:users!addressee_id(*)
      `)
      .single();

    if (error) throw error;
    return data;
  }

  // Accept a friend request
  static async acceptFriendRequest(friendshipId: string): Promise<Friendship> {
    const { data, error } = await supabase
      .from('friendships')
      .update({ 
        status: 'accepted',
        updated_at: new Date().toISOString()
      })
      .eq('id', friendshipId)
      .select(`
        *,
        requester:users!requester_id(*),
        addressee:users!addressee_id(*)
      `)
      .single();

    if (error) throw error;
    return data;
  }

  // Decline a friend request
  static async declineFriendRequest(friendshipId: string): Promise<void> {
    const { error } = await supabase
      .from('friendships')
      .delete()
      .eq('id', friendshipId);

    if (error) throw error;
  }

  // Block a user
  static async blockUser(requesterId: string, addresseeId: string): Promise<Friendship> {
    // First check if friendship exists
    const { data: existingFriendship } = await supabase
      .from('friendships')
      .select('*')
      .or(`and(requester_id.eq.${requesterId},addressee_id.eq.${addresseeId}),and(requester_id.eq.${addresseeId},addressee_id.eq.${requesterId})`)
      .single();

    if (existingFriendship) {
      // Update existing friendship to blocked
      const { data, error } = await supabase
        .from('friendships')
        .update({ 
          status: 'blocked',
          updated_at: new Date().toISOString()
        })
        .eq('id', existingFriendship.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } else {
      // Create new blocked relationship
      const { data, error } = await supabase
        .from('friendships')
        .insert({
          requester_id: requesterId,
          addressee_id: addresseeId,
          status: 'blocked'
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    }
  }

  // Remove friend
  static async removeFriend(friendshipId: string): Promise<void> {
    const { error } = await supabase
      .from('friendships')
      .delete()
      .eq('id', friendshipId);

    if (error) throw error;
  }

  // Get user's friends (accepted friendships)
  static async getFriends(userId: string): Promise<UserProfile[]> {
    const { data, error } = await supabase
      .from('friendships')
      .select(`
        *,
        requester:users!requester_id(*),
        addressee:users!addressee_id(*)
      `)
      .eq('status', 'accepted')
      .or(`requester_id.eq.${userId},addressee_id.eq.${userId}`);

    if (error) throw error;

    // Map to get the friend (not the current user)
    const friends = (data || []).map(friendship => {
      return friendship.requester_id === userId 
        ? friendship.addressee 
        : friendship.requester;
    }).filter(Boolean) as UserProfile[];

    // Sort by level (highest to lowest)
    return friends.sort((a, b) => b.level - a.level);
  }

  // Get pending friend requests received by user
  static async getPendingRequests(userId: string): Promise<FriendRequest[]> {
    const { data, error } = await supabase
      .from('friendships')
      .select(`
        *,
        requester:users!requester_id(*)
      `)
      .eq('addressee_id', userId)
      .eq('status', 'pending');

    if (error) throw error;
    return data || [];
  }

  // Get pending friend requests sent by user
  static async getSentRequests(userId: string): Promise<Friendship[]> {
    const { data, error } = await supabase
      .from('friendships')
      .select(`
        *,
        addressee:users!addressee_id(*)
      `)
      .eq('requester_id', userId)
      .eq('status', 'pending');

    if (error) throw error;
    return data || [];
  }

  // Search users by username or display name
  static async searchUsers(query: string, currentUserId: string): Promise<UserProfile[]> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .neq('id', currentUserId) // Exclude current user
      .or(`username.ilike.%${query}%,display_name.ilike.%${query}%`)
      .limit(20);

    if (error) throw error;
    return data || [];
  }

  // Get friendship status between two users
  static async getFriendshipStatus(userId1: string, userId2: string): Promise<Friendship | null> {
    const { data, error } = await supabase
      .from('friendships')
      .select('*')
      .or(`and(requester_id.eq.${userId1},addressee_id.eq.${userId2}),and(requester_id.eq.${userId2},addressee_id.eq.${userId1})`)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data || null;
  }

  // Get recent friends (last 5 friends by friendship date)
  static async getRecentFriends(userId: string, limit: number = 5): Promise<UserProfile[]> {
    const { data, error } = await supabase
      .from('friendships')
      .select(`
        *,
        requester:users!requester_id(*),
        addressee:users!addressee_id(*)
      `)
      .eq('status', 'accepted')
      .or(`requester_id.eq.${userId},addressee_id.eq.${userId}`)
      .order('updated_at', { ascending: false })
      .limit(limit);

    if (error) throw error;

    // Map to get the friend (not the current user)
    const friends = (data || []).map(friendship => {
      return friendship.requester_id === userId 
        ? friendship.addressee 
        : friendship.requester;
    }).filter(Boolean) as UserProfile[];

    return friends;
  }

  // Get friend count
  static async getFriendCount(userId: string): Promise<number> {
    const { count, error } = await supabase
      .from('friendships')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'accepted')
      .or(`requester_id.eq.${userId},addressee_id.eq.${userId}`);

    if (error) throw error;
    return count || 0;
  }
}
