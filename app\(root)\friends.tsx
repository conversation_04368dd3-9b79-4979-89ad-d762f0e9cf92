import { useAuth } from '@/hooks/useAuth';
import { FriendService } from '@/services/FriendService';
import { FriendRequest, UserProfile } from '@/types';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Image,
  RefreshControl,
  ScrollView,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

const Friends = () => {
  const { user } = useAuth();
  const [friends, setFriends] = useState<UserProfile[]>([]);
  const [pendingRequests, setPendingRequests] = useState<FriendRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'friends' | 'requests'>('friends');
  const [processingRequest, setProcessingRequest] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    if (!user) return;

    try {
      const [friendsData, requestsData] = await Promise.all([
        FriendService.getFriends(user.id),
        FriendService.getPendingRequests(user.id)
      ]);

      setFriends(friendsData);
      setPendingRequests(requestsData);
    } catch (error) {
      console.error('Error loading friends data:', error);
      Alert.alert('Error', 'Failed to load friends data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleAcceptRequest = async (requestId: string) => {
    setProcessingRequest(requestId);
    try {
      await FriendService.acceptFriendRequest(requestId);
      await loadData();
      Alert.alert('Success', 'Friend request accepted!');
    } catch (error) {
      console.error('Error accepting friend request:', error);
      Alert.alert('Error', 'Failed to accept friend request');
    } finally {
      setProcessingRequest(null);
    }
  };

  const handleDeclineRequest = async (requestId: string) => {
    setProcessingRequest(requestId);
    try {
      await FriendService.declineFriendRequest(requestId);
      await loadData();
    } catch (error) {
      console.error('Error declining friend request:', error);
      Alert.alert('Error', 'Failed to decline friend request');
    } finally {
      setProcessingRequest(null);
    }
  };





  const renderFriendItem = ({ item }: { item: UserProfile }) => (
    <View className="flex-row items-center p-4 bg-white rounded-2xl mb-3 shadow-sm">
      <View className="mr-3">
        {item.avatar_url ? (
          <Image
            source={{ uri: item.avatar_url }}
            className="w-14 h-14 rounded-full"
          />
        ) : (
          <View className="w-14 h-14 rounded-full bg-gray-300 justify-center items-center">
            <Ionicons name="person" size={28} color="#6B7280" />
          </View>
        )}
      </View>

      <View className="flex-1">
        <Text className="font-semibold text-gray-900 text-base" numberOfLines={1}>
          {item.display_name || item.username}
        </Text>
        <Text className="text-sm text-gray-500 mb-1">@{item.username}</Text>
        <View className="flex-row items-center">
          <View className="flex-row items-center mr-4">
            <Ionicons name="trophy" size={14} color="#F59E0B" />
            <Text className="text-xs text-gray-600 ml-1">Level {item.level}</Text>
          </View>
          <View className="flex-row items-center">
            <Ionicons name="star" size={14} color="#3B82F6" />
            <Text className="text-xs text-gray-600 ml-1">{item.total_score}</Text>
          </View>
        </View>
      </View>

      <View className="items-center">
        <View className="w-3 h-3 bg-green-500 rounded-full mb-1" />
        <Text className="text-xs text-gray-500">Online</Text>
      </View>
    </View>
  );



  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50 justify-center items-center">
        <ActivityIndicator size="large" color="#3B82F6" />
        <Text className="text-gray-500 mt-4">Loading friends...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="bg-white px-6 py-4 border-b border-gray-200">
        <View className="flex-row items-center justify-between mb-4">
          <View className="flex-row items-center">
            <TouchableOpacity onPress={() => router.back()} className="mr-4">
              <Ionicons name="arrow-back" size={24} color="#374151" />
            </TouchableOpacity>
            <Text className="text-xl font-bold text-gray-900">Friends</Text>
          </View>
          
          <TouchableOpacity
            onPress={() => router.push('/(root)/add-friend')}
            className="bg-blue-500 rounded-full px-4 py-2 flex-row items-center"
          >
            <Ionicons name="person-add" size={16} color="white" />
            <Text className="text-white font-medium ml-1">Add</Text>
          </TouchableOpacity>
        </View>

        {/* Tabs */}
        <View className="flex-row bg-gray-100 rounded-xl p-1">
          <TouchableOpacity
            onPress={() => setActiveTab('friends')}
            className={`flex-1 py-2 rounded-lg ${
              activeTab === 'friends' ? 'bg-white shadow-sm' : ''
            }`}
          >
            <Text className={`text-center font-medium ${
              activeTab === 'friends' ? 'text-blue-600' : 'text-gray-600'
            }`}>
              Friends ({friends.length})
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            onPress={() => setActiveTab('requests')}
            className={`flex-1 py-2 rounded-lg ${
              activeTab === 'requests' ? 'bg-white shadow-sm' : ''
            }`}
          >
            <Text className={`text-center font-medium ${
              activeTab === 'requests' ? 'text-blue-600' : 'text-gray-600'
            }`}>
              Requests ({pendingRequests?.length || 0})
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Content */}
      <ScrollView 
        className="flex-1 px-6 pt-4"
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {activeTab === 'friends' ? (
          friends.length > 0 ? (
            friends.map((friend) => (
              <View key={friend.id}>
                {renderFriendItem({ item: friend })}
              </View>
            ))
          ) : (
            <View className="flex-1 justify-center items-center py-20">
              <Ionicons name="people" size={64} color="#9CA3AF" />
              <Text className="text-gray-500 text-center mt-4 text-lg">
                No friends yet
              </Text>
              <Text className="text-gray-400 text-center text-sm mt-2 mb-6">
                Add some friends to see them here
              </Text>
              <TouchableOpacity
                onPress={() => router.push('/(root)/add-friend')}
                className="bg-blue-500 rounded-2xl px-6 py-3"
              >
                <Text className="text-white font-semibold">Add Friends</Text>
              </TouchableOpacity>
            </View>
          )
        ) : (
          <View className="py-4">
            <Text className="text-lg font-semibold text-gray-900 mb-4">
              Friend Requests ({pendingRequests.length})
            </Text>
            {pendingRequests.length > 0 ? (
              pendingRequests.map((request) => (
                <View key={request.id} className="bg-white rounded-2xl p-4 mb-3 shadow-sm">
                  <View className="flex-row items-center">
                    <View className="mr-3">
                      {request.requester.avatar_url ? (
                        <Image
                          source={{ uri: request.requester.avatar_url }}
                          className="w-14 h-14 rounded-full"
                        />
                      ) : (
                        <View className="w-14 h-14 rounded-full bg-gray-300 justify-center items-center">
                          <Ionicons name="person" size={28} color="#6B7280" />
                        </View>
                      )}
                    </View>

                    <View className="flex-1">
                      <Text className="font-semibold text-gray-900 text-base" numberOfLines={1}>
                        {request.requester.display_name || request.requester.username}
                      </Text>
                      <Text className="text-sm text-gray-500 mb-1">@{request.requester.username}</Text>
                      <Text className="text-xs text-gray-400">
                        {new Date(request.created_at).toLocaleDateString()}
                      </Text>
                    </View>

                    <View className="flex-row space-x-2">
                      <TouchableOpacity
                        onPress={() => handleAcceptRequest(request.id)}
                        disabled={processingRequest === request.id}
                        className="bg-green-500 rounded-full px-3 py-2"
                      >
                        {processingRequest === request.id ? (
                          <ActivityIndicator size="small" color="white" />
                        ) : (
                          <Ionicons name="checkmark" size={16} color="white" />
                        )}
                      </TouchableOpacity>

                      <TouchableOpacity
                        onPress={() => handleDeclineRequest(request.id)}
                        disabled={processingRequest === request.id}
                        className="bg-red-500 rounded-full px-3 py-2"
                      >
                        <Ionicons name="close" size={16} color="white" />
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              ))
            ) : (
              <View className="flex-1 justify-center items-center py-20">
                <Ionicons name="mail" size={64} color="#9CA3AF" />
                <Text className="text-gray-500 text-center mt-4 text-lg">
                  No pending requests
                </Text>
                <Text className="text-gray-400 text-center text-sm mt-2">
                  Friend requests will appear here
                </Text>
              </View>
            )}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default Friends;
