import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { downloadManager, DownloadProgress } from '../../../lib/downloadUtils';
import { supabase } from '../../../lib/supabase';

interface CourseFile {
  name: string;
  path: string;
  type: 'pdf' | 'image' | 'document' | 'other';
  isOffline?: boolean;
  size?: number;
  lastModified?: string;
  isDownloaded?: boolean;
  downloadProgress?: number;
}

interface CourseSection {
  name: string;
  files: CourseFile[];
}

const CourseDetailScreen = () => {
  const { categoryId, categoryName, categoryDescription } = useLocalSearchParams();
  const [sections, setSections] = useState<CourseSection[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [downloadingFiles, setDownloadingFiles] = useState<Set<string>>(new Set());

  const categoryNameStr = Array.isArray(categoryName) ? categoryName[0] : categoryName;
  const categoryDescStr = Array.isArray(categoryDescription) ? categoryDescription[0] : categoryDescription;
  const categoryIdStr = Array.isArray(categoryId) ? categoryId[0] : categoryId;

  useEffect(() => {
    loadCourseFiles();
  }, [categoryIdStr]);

  const loadCourseFiles = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!categoryNameStr) {
        throw new Error('Category name is required');
      }
      const categoryPath = categoryNameStr.toLowerCase();
      console.log('Loading course files for category:', categoryNameStr, 'using path:', categoryPath);

      const expectedSections = ['slides', 'pastquestions', 'labs'];
      const courseSections: CourseSection[] = [];

      for (const sectionName of expectedSections) {
        try {
          const sectionPath = `${categoryPath}/${sectionName}`;
          console.log(`Loading section: ${sectionName} from path: ${sectionPath}`);

          // List files in each section using storage API
          const { data: files, error: listError } = await supabase.storage
            .from('courses')
            .list(sectionPath, {
              limit: 100,
              offset: 0,
            });

          if (listError) {
            console.warn(`Error loading ${sectionName}:`, listError);
            continue;
          }

          console.log(`Found ${files?.length || 0} files in ${sectionName}`);

          if (files && files.length > 0) {
            const courseFiles: CourseFile[] = await Promise.all(
              files
                .filter(file => file.name !== '.emptyFolderPlaceholder')
                .map(async (file) => {
                  const isDownloaded = await downloadManager.isFileDownloaded(
                    categoryNameStr || '',
                    sectionName,
                    file.name
                  );

                  return {
                    name: file.name,
                    path: `${sectionPath}/${file.name}`,
                    type: getFileType(file.name),
                    size: file.metadata?.size,
                    lastModified: file.updated_at,
                    isDownloaded,
                  };
                })
            );

            if (courseFiles.length > 0) {
              courseSections.push({
                name: sectionName.charAt(0).toUpperCase() + sectionName.slice(1),
                files: courseFiles,
              });
            }
          }
        } catch (sectionError) {
          console.warn(`Error loading section ${sectionName}:`, sectionError);
        }
      }

      setSections(courseSections);
    } catch (err) {
      console.error('Error loading course files:', err);
      setError((err as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const getFileType = (fileName: string): 'pdf' | 'image' | 'document' | 'other' => {
    const extension = fileName.toLowerCase().split('.').pop();

    if (extension === 'pdf') return 'pdf';
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(extension || '')) return 'image';
    if (['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'].includes(extension || '')) return 'document';
    return 'other';
  };

  const getFileIcon = (type: string, fileName?: string) => {
    switch (type) {
      case 'pdf':
        return 'document-text';
      case 'image':
        return 'image';
      case 'document':
        const ext = fileName?.toLowerCase().split('.').pop();
        if (['ppt', 'pptx'].includes(ext || '')) return 'easel';
        if (['xls', 'xlsx'].includes(ext || '')) return 'grid';
        return 'document-text';
      default:
        return 'document';
    }
  };

  const handleDownload = async (file: CourseFile, sectionName: string) => {
    try {
      if (!categoryNameStr) {
        Alert.alert('Error', 'Category information is missing');
        return;
      }

      // Add to downloading files
      setDownloadingFiles(prev => new Set(prev).add(file.name));

      // Get the file URL from Supabase
      const { data } = supabase.storage
        .from('courses')
        .getPublicUrl(file.path);

      if (!data?.publicUrl) {
        Alert.alert('Error', 'Could not get file URL');
        return;
      }

      // Update file progress
      const updateFileProgress = (progress: DownloadProgress) => {
        setSections(prevSections =>
          prevSections.map(section => ({
            ...section,
            files: section.files.map(f =>
              f.name === file.name
                ? { ...f, downloadProgress: progress.progress * 100 }
                : f
            )
          }))
        );
      };

      // Download the file
      const downloadPath = await downloadManager.downloadFile({
        url: data.publicUrl,
        fileName: file.name,
        categoryName: categoryNameStr,
        sectionName: sectionName.toLowerCase(),
        onProgress: updateFileProgress,
      });

      if (downloadPath) {
        // Update file as downloaded
        setSections(prevSections =>
          prevSections.map(section => ({
            ...section,
            files: section.files.map(f =>
              f.name === file.name
                ? { ...f, isDownloaded: true, downloadProgress: undefined }
                : f
            )
          }))
        );
      }
    } catch (error) {
      console.error('Download error:', error);
      Alert.alert('Download Failed', 'An error occurred while downloading the file.');
    } finally {
      // Remove from downloading files
      setDownloadingFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(file.name);
        return newSet;
      });
    }
  };

  const handleFilePress = async (file: CourseFile) => {
    try {
      console.log('Opening file:', file.name, 'at path:', file.path);

      const { data } = supabase.storage
        .from('courses')
        .getPublicUrl(file.path);

      console.log('File URL:', data?.publicUrl);

      if (!data?.publicUrl) {
        Alert.alert('Error', 'Could not get file URL');
        return;
      }
      
      if (file.type === 'pdf') {
        router.push({
          pathname: '/course/pdf-viewer',
          params: {
            url: data.publicUrl,
            fileName: file.name,
          },
        });
      } else if (file.type === 'image') {
        router.push({
          pathname: '/course/image-viewer',
          params: {
            url: data.publicUrl,
            fileName: file.name,
          },
        });
      } else if (file.type === 'document') {
        router.push({
          pathname: '/course/simple-document-viewer',
          params: {
            url: data.publicUrl,
            fileName: file.name,
            fileType: file.type,
          },
        });
      } else {
        Alert.alert('Unsupported File', 'This file type is not supported for viewing.');
      }
    } catch (err) {
      console.error('Error opening file:', err);
      Alert.alert('Error', 'Could not open file');
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 justify-center items-center">
          <Text className="text-lg">Loading course materials...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 justify-center items-center p-4">
          <Text className="text-red-500 text-center mb-4">{error}</Text>
          <TouchableOpacity
            onPress={loadCourseFiles}
            className="bg-blue-500 px-6 py-3 rounded-lg"
          >
            <Text className="text-white font-semibold">Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center p-4 border-b border-gray-200">
        <TouchableOpacity
          onPress={() => router.back()}
          className="mr-4"
        >
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <View className="flex-1">
          <Text className="text-xl font-bold">{categoryNameStr}</Text>
          <Text className="text-gray-600 text-sm">{categoryDescStr}</Text>
        </View>
      </View>

      <ScrollView className="flex-1">
        {sections.length === 0 ? (
          <View className="flex-1 justify-center items-center p-8">
            <Ionicons name="folder-open-outline" size={64} color="#9CA3AF" />
            <Text className="text-gray-500 text-center mt-4">
              No course materials found for this category.
            </Text>
            <TouchableOpacity
              onPress={loadCourseFiles}
              className="bg-blue-500 px-6 py-3 rounded-lg mt-4"
            >
              <Text className="text-white font-semibold">Refresh</Text>
            </TouchableOpacity>
          </View>
        ) : (
          sections.map((section, sectionIndex) => (
            <View key={section.name} className="p-4">
              <Text className="text-lg font-semibold mb-3 text-gray-800">
                {section.name} ({section.files.length})
              </Text>
              
              {section.files.map((file, fileIndex) => (
                <View
                  key={`${sectionIndex}-${fileIndex}`}
                  className="bg-gray-50 rounded-lg mb-2 overflow-hidden"
                >
                  <TouchableOpacity
                    onPress={() => handleFilePress(file)}
                    className="flex-row items-center p-3"
                  >
                    <Ionicons
                      name={getFileIcon(file.type, file.name) as any}
                      size={24}
                      color={
                        file.type === 'pdf' ? '#DC2626' :
                        file.type === 'image' ? '#059669' :
                        file.type === 'document' ? '#2B579A' :
                        '#6B7280'
                      }
                      className="mr-3"
                    />
                    <View className="flex-1">
                      <View className="flex-row items-center">
                        <Text className="font-medium text-gray-900 flex-1" numberOfLines={1}>
                          {file.name}
                        </Text>
                        {file.isDownloaded && (
                          <Ionicons name="checkmark-circle" size={16} color="#059669" className="ml-2" />
                        )}
                        {file.isOffline && (
                          <Ionicons name="cloud-offline" size={16} color="#059669" className="ml-2" />
                        )}
                      </View>
                      <Text className="text-sm text-gray-500">
                        {file.type.toUpperCase()} • {formatFileSize(file.size)}
                        {file.isDownloaded && ' • Downloaded'}
                        {file.isOffline && ' • Offline'}
                      </Text>
                    </View>
                    <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
                  </TouchableOpacity>

                  {/* Download Progress Bar */}
                  {file.downloadProgress !== undefined && (
                    <View className="px-3 pb-2">
                      <View className="bg-gray-200 rounded-full h-2">
                        <View
                          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${file.downloadProgress}%` }}
                        />
                      </View>
                      <Text className="text-xs text-gray-500 mt-1">
                        Downloading... {Math.round(file.downloadProgress)}%
                      </Text>
                    </View>
                  )}

                  {/* Download Button */}
                  <View className="flex-row border-t border-gray-200">
                    <TouchableOpacity
                      onPress={() => handleDownload(file, section.name)}
                      disabled={downloadingFiles.has(file.name) || file.isDownloaded}
                      className={`flex-1 flex-row items-center justify-center p-3 ${
                        file.isDownloaded
                          ? 'bg-green-50'
                          : downloadingFiles.has(file.name)
                          ? 'bg-gray-100'
                          : 'bg-blue-50'
                      }`}
                    >
                      <Ionicons
                        name={
                          file.isDownloaded
                            ? 'checkmark-circle'
                            : downloadingFiles.has(file.name)
                            ? 'hourglass'
                            : 'download'
                        }
                        size={16}
                        color={
                          file.isDownloaded
                            ? '#059669'
                            : downloadingFiles.has(file.name)
                            ? '#6B7280'
                            : '#2563EB'
                        }
                        className="mr-2"
                      />
                      <Text className={`text-sm font-medium ${
                        file.isDownloaded
                          ? 'text-green-700'
                          : downloadingFiles.has(file.name)
                          ? 'text-gray-500'
                          : 'text-blue-700'
                      }`}>
                        {file.isDownloaded
                          ? 'Downloaded'
                          : downloadingFiles.has(file.name)
                          ? 'Downloading...'
                          : 'Download'
                        }
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </View>
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default CourseDetailScreen;
