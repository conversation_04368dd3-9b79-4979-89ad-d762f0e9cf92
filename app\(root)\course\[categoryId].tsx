import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { supabase } from '../../../lib/supabase';

interface CourseFile {
  name: string;
  path: string;
  type: 'pdf' | 'image' | 'document' | 'other';
  isOffline?: boolean;
  size?: number;
  lastModified?: string;
}

interface CourseSection {
  name: string;
  files: CourseFile[];
}

const CourseDetailScreen = () => {
  const { categoryId, categoryName, categoryDescription } = useLocalSearchParams();
  const [sections, setSections] = useState<CourseSection[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const categoryNameStr = Array.isArray(categoryName) ? categoryName[0] : categoryName;
  const categoryDescStr = Array.isArray(categoryDescription) ? categoryDescription[0] : categoryDescription;
  const categoryIdStr = Array.isArray(categoryId) ? categoryId[0] : categoryId;

  useEffect(() => {
    loadCourseFiles();
  }, [categoryIdStr]);

  const loadCourseFiles = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!categoryNameStr) {
        throw new Error('Category name is required');
      }
      const categoryPath = categoryNameStr.toLowerCase();
      console.log('Loading course files for category:', categoryNameStr, 'using path:', categoryPath);

      const expectedSections = ['slides', 'pastquestions', 'labs'];
      const courseSections: CourseSection[] = [];

      for (const sectionName of expectedSections) {
        try {
          const sectionPath = `${categoryPath}/${sectionName}`;
          console.log(`Loading section: ${sectionName} from path: ${sectionPath}`);

          // List files in each section using storage API
          const { data: files, error: listError } = await supabase.storage
            .from('courses')
            .list(sectionPath, {
              limit: 100,
              offset: 0,
            });

          if (listError) {
            console.warn(`Error loading ${sectionName}:`, listError);
            continue;
          }

          console.log(`Found ${files?.length || 0} files in ${sectionName}`);

          if (files && files.length > 0) {
            const courseFiles: CourseFile[] = files
              .filter(file => file.name !== '.emptyFolderPlaceholder')
              .map(file => ({
                name: file.name,
                path: `${sectionPath}/${file.name}`,
                type: getFileType(file.name),
                size: file.metadata?.size,
                lastModified: file.updated_at,
              }));

            if (courseFiles.length > 0) {
              courseSections.push({
                name: sectionName.charAt(0).toUpperCase() + sectionName.slice(1),
                files: courseFiles,
              });
            }
          }
        } catch (sectionError) {
          console.warn(`Error loading section ${sectionName}:`, sectionError);
        }
      }

      setSections(courseSections);
    } catch (err) {
      console.error('Error loading course files:', err);
      setError((err as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const getFileType = (fileName: string): 'pdf' | 'image' | 'document' | 'other' => {
    const extension = fileName.toLowerCase().split('.').pop();

    if (extension === 'pdf') return 'pdf';
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(extension || '')) return 'image';
    if (['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'].includes(extension || '')) return 'document';
    return 'other';
  };

  const getFileIcon = (type: string, fileName?: string) => {
    switch (type) {
      case 'pdf':
        return 'document-text';
      case 'image':
        return 'image';
      case 'document':
        const ext = fileName?.toLowerCase().split('.').pop();
        if (['ppt', 'pptx'].includes(ext || '')) return 'easel';
        if (['xls', 'xlsx'].includes(ext || '')) return 'grid';
        return 'document-text';
      default:
        return 'document';
    }
  };

  const handleFilePress = async (file: CourseFile) => {
    try {
      console.log('Opening file:', file.name, 'at path:', file.path);

      const { data } = supabase.storage
        .from('courses')
        .getPublicUrl(file.path);

      console.log('File URL:', data?.publicUrl);

      if (!data?.publicUrl) {
        Alert.alert('Error', 'Could not get file URL');
        return;
      }
      
      if (file.type === 'pdf') {
        router.push({
          pathname: '/course/pdf-viewer',
          params: {
            url: data.publicUrl,
            fileName: file.name,
          },
        });
      } else if (file.type === 'image') {
        router.push({
          pathname: '/course/image-viewer',
          params: {
            url: data.publicUrl,
            fileName: file.name,
          },
        });
      } else if (file.type === 'document') {
        router.push({
          pathname: '/course/simple-document-viewer',
          params: {
            url: data.publicUrl,
            fileName: file.name,
            fileType: file.type,
          },
        });
      } else {
        Alert.alert('Unsupported File', 'This file type is not supported for viewing.');
      }
    } catch (err) {
      console.error('Error opening file:', err);
      Alert.alert('Error', 'Could not open file');
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 justify-center items-center">
          <Text className="text-lg">Loading course materials...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 justify-center items-center p-4">
          <Text className="text-red-500 text-center mb-4">{error}</Text>
          <TouchableOpacity
            onPress={loadCourseFiles}
            className="bg-blue-500 px-6 py-3 rounded-lg"
          >
            <Text className="text-white font-semibold">Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center p-4 border-b border-gray-200">
        <TouchableOpacity
          onPress={() => router.back()}
          className="mr-4"
        >
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <View className="flex-1">
          <Text className="text-xl font-bold">{categoryNameStr}</Text>
          <Text className="text-gray-600 text-sm">{categoryDescStr}</Text>
        </View>
      </View>

      <ScrollView className="flex-1">
        {sections.length === 0 ? (
          <View className="flex-1 justify-center items-center p-8">
            <Ionicons name="folder-open-outline" size={64} color="#9CA3AF" />
            <Text className="text-gray-500 text-center mt-4">
              No course materials found for this category.
            </Text>
            <TouchableOpacity
              onPress={loadCourseFiles}
              className="bg-blue-500 px-6 py-3 rounded-lg mt-4"
            >
              <Text className="text-white font-semibold">Refresh</Text>
            </TouchableOpacity>
          </View>
        ) : (
          sections.map((section, sectionIndex) => (
            <View key={section.name} className="p-4">
              <Text className="text-lg font-semibold mb-3 text-gray-800">
                {section.name} ({section.files.length})
              </Text>
              
              {section.files.map((file, fileIndex) => (
                <TouchableOpacity
                  key={`${sectionIndex}-${fileIndex}`}
                  onPress={() => handleFilePress(file)}
                  className="flex-row items-center p-3 bg-gray-50 rounded-lg mb-2"
                >
                  <Ionicons
                    name={getFileIcon(file.type, file.name) as any}
                    size={24}
                    color={
                      file.type === 'pdf' ? '#DC2626' :
                      file.type === 'image' ? '#059669' :
                      file.type === 'document' ? '#2B579A' :
                      '#6B7280'
                    }
                    className="mr-3"
                  />
                  <View className="flex-1">
                    <View className="flex-row items-center">
                      <Text className="font-medium text-gray-900 flex-1" numberOfLines={1}>
                        {file.name}
                      </Text>
                      {file.isOffline && (
                        <Ionicons name="cloud-offline" size={16} color="#059669" className="ml-2" />
                      )}
                    </View>
                    <Text className="text-sm text-gray-500">
                      {file.type.toUpperCase()} • {formatFileSize(file.size)}
                      {file.isOffline && ' • Offline'}
                    </Text>
                  </View>
                  <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
                </TouchableOpacity>
              ))}
            </View>
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default CourseDetailScreen;
