import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useState } from 'react';
import { Alert, Linking, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

const SimpleDocumentViewer = () => {
  const { url, fileName, fileType } = useLocalSearchParams();
  const [loading, setLoading] = useState(false);

  const urlStr = Array.isArray(url) ? url[0] : url;
  const fileNameStr = Array.isArray(fileName) ? fileName[0] : fileName;
  const fileTypeStr = Array.isArray(fileType) ? fileType[0] : fileType;

  const getFileTypeIcon = (type: string, fileName: string) => {
    const ext = fileName.toLowerCase().split('.').pop();
    
    if (['doc', 'docx'].includes(ext || '')) return 'document-text';
    if (['ppt', 'pptx'].includes(ext || '')) return 'easel';
    if (['xls', 'xlsx'].includes(ext || '')) return 'grid';
    if (ext === 'pdf') return 'document';
    return 'document-outline';
  };

  const getFileTypeColor = (type: string, fileName: string) => {
    const ext = fileName.toLowerCase().split('.').pop();
    
    if (['doc', 'docx'].includes(ext || '')) return '#2B579A';
    if (['ppt', 'pptx'].includes(ext || '')) return '#D24726';
    if (['xls', 'xlsx'].includes(ext || '')) return '#217346';
    if (ext === 'pdf') return '#DC2626';
    return '#6B7280';
  };

  const handleOpenInBrowser = async () => {
    try {
      setLoading(true);
      
      if (!urlStr) {
        Alert.alert('Error', 'File URL not available');
        return;
      }

      // For documents, use Google Docs viewer
      const googleDocsUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(urlStr)}`;
      
      const canOpen = await Linking.canOpenURL(googleDocsUrl);
      if (canOpen) {
        await Linking.openURL(googleDocsUrl);
      } else {
        Alert.alert('Error', 'Cannot open document viewer');
      }
    } catch (error) {
      console.error('Error opening document:', error);
      Alert.alert('Error', 'Failed to open document');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDirectly = async () => {
    try {
      setLoading(true);
      
      if (!urlStr) {
        Alert.alert('Error', 'File URL not available');
        return;
      }

      const canOpen = await Linking.canOpenURL(urlStr);
      if (canOpen) {
        await Linking.openURL(urlStr);
      } else {
        Alert.alert('Error', 'Cannot open file directly');
      }
    } catch (error) {
      console.error('Error opening file:', error);
      Alert.alert('Error', 'Failed to open file');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = () => {
    Alert.alert(
      'Download File',
      'This will open the file in your browser where you can download it.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open', onPress: handleOpenDirectly },
      ]
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center p-4 border-b border-gray-200">
        <TouchableOpacity
          onPress={() => router.back()}
          className="mr-4"
        >
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <View className="flex-1">
          <Text className="text-lg font-semibold" numberOfLines={1}>
            {fileNameStr}
          </Text>
          <Text className="text-sm text-gray-600">
            {fileTypeStr?.toUpperCase()} Document
          </Text>
        </View>
      </View>

      {/* Content */}
      <View className="flex-1 justify-center items-center p-6">
        <Ionicons 
          name={getFileTypeIcon(fileTypeStr || '', fileNameStr || '')} 
          size={100} 
          color={getFileTypeColor(fileTypeStr || '', fileNameStr || '')} 
        />
        
        <Text className="text-2xl font-bold mt-6 mb-2 text-center">
          {fileNameStr}
        </Text>
        
        <Text className="text-gray-600 text-center mb-8">
          Choose how you'd like to open this document
        </Text>

        <View className="w-full max-w-sm space-y-4">
          {/* Google Docs Viewer */}
          <TouchableOpacity
            onPress={handleOpenInBrowser}
            disabled={loading}
            className={`py-4 px-6 rounded-lg border-2 border-blue-500 ${
              loading ? 'opacity-50' : ''
            }`}
          >
            <View className="flex-row items-center justify-center">
              <Ionicons name="globe-outline" size={24} color="#3B82F6" className="mr-3" />
              <Text className="text-blue-500 font-semibold text-lg">
                Open in Browser Viewer
              </Text>
            </View>
            <Text className="text-sm text-gray-500 text-center mt-1">
              View using Google Docs viewer
            </Text>
          </TouchableOpacity>

          {/* Direct Download */}
          <TouchableOpacity
            onPress={handleDownload}
            disabled={loading}
            className={`py-4 px-6 rounded-lg bg-green-500 ${
              loading ? 'opacity-50' : ''
            }`}
          >
            <View className="flex-row items-center justify-center">
              <Ionicons name="download-outline" size={24} color="white" className="mr-3" />
              <Text className="text-white font-semibold text-lg">
                Download File
              </Text>
            </View>
            <Text className="text-sm text-green-100 text-center mt-1">
              Download to your device
            </Text>
          </TouchableOpacity>

          {/* Open Directly */}
          <TouchableOpacity
            onPress={handleOpenDirectly}
            disabled={loading}
            className={`py-4 px-6 rounded-lg bg-gray-500 ${
              loading ? 'opacity-50' : ''
            }`}
          >
            <View className="flex-row items-center justify-center">
              <Ionicons name="open-outline" size={24} color="white" className="mr-3" />
              <Text className="text-white font-semibold text-lg">
                Open Directly
              </Text>
            </View>
            <Text className="text-sm text-gray-200 text-center mt-1">
              Let your device handle the file
            </Text>
          </TouchableOpacity>
        </View>

        <View className="mt-8 p-4 bg-blue-50 rounded-lg">
          <Text className="text-sm text-blue-800 text-center">
            💡 Tip: For the best experience, install Microsoft Office or Google Docs apps on your device
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default SimpleDocumentViewer;
