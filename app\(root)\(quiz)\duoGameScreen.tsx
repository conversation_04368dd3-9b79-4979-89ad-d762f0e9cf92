import { router, useLocalSearchParams } from 'expo-router';
import { useEffect, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { GameResults } from '../../../components/GameResults';
import { useAuth } from '../../../hooks/useAuth';
import { useGame } from '../../../hooks/useGame';
import { GameService } from '../../../services/GameService';
import { QuizService } from '../../../services/QuizService';

const duoGameScreen = (props: any) => {
    const { quizId, onGameEnd } = useLocalSearchParams();
    const quizIdStr = Array.isArray(quizId) ? quizId[0] : quizId;
    const { user } = useAuth();
    const [gameId, setGameId] = useState<string | null>(null);
    const [waiting, setWaiting] = useState(true);
    const [questions, setQuestions] = useState<any[]>([]);
    const [questionIndex, setQuestionIndex] = useState(0);
    const [questionAnswered, setQuestionAnswered] = useState(false);
    const [answerResult, setAnswerResult] = useState<'correct' | 'wrong' | null>(null);
    const [showResults, setShowResults] = useState(false);
    const [refreshing, setRefreshing] = useState(false);

    // Always call useGame hook, but pass undefined when user is null
    const {
      game,
      participants,
      currentQuestion,
      setCurrentQuestion,
      timeRemaining,
      joinGame,
      leaveGame,
      submitAnswer,
      startTimer,
      refreshGameData,
      loading,
      error,
    } = useGame(gameId ?? undefined);

    // Rest of your useEffects and logic...
    useEffect(() => {
      if (!user || !quizIdStr) return;

      const startMatchmaking = async () => {
        setWaiting(true);
        try {
          const session = await GameService.findOrCreateGame(quizIdStr, 'battle', user.id);
          if (!session || !session.id) {
            console.error('No game session returned:', session);
            return;
          }
          console.log('Found/created game session:', session.id, 'current_players:', session.current_players);
          setGameId(session.id);
          await joinGame(session.id, user.id);

          // Immediately fetch participants after joining
          const updatedParticipants = await GameService.getGameParticipants(session.id);
          console.log('Participants after joining:', updatedParticipants);
        } catch (err) {
          console.log('Matchmaking error:', err);
        }
      };
      startMatchmaking();
    }, [quizId, user?.id]);
  
    useEffect(() => {
      const fetchQuestions = async () => {
        try {
          let qs: any[] = [];

          // If we have a game with random_session_id in settings, handle random questions
          if (game?.settings?.random_session_id) {
            const sessionId = game.settings.random_session_id;

            // Check if questions are already stored for this session
            qs = QuizService.getRandomSessionQuestions(sessionId);

            // If no questions stored, generate them (first player to join generates for all)
            if (qs.length === 0) {
              console.log('Generating questions for session:', sessionId);

              if (sessionId === 'random_all_categories') {
                // Random questions from all categories
                qs = await QuizService.getRandomQuestions(10);
              } else if (sessionId.startsWith('random_category_')) {
                // Random questions from specific category
                const categoryId = sessionId.replace('random_category_', '');
                qs = await QuizService.getRandomQuestionsByCategory(categoryId, 10);
              }

              // Store questions for this session so other players get the same questions
              if (qs.length > 0) {
                QuizService.storeQuestionsForSession(sessionId, qs);
              }
            }
          } else {
            // Regular quiz questions
            qs = await QuizService.getQuizQuestions(quizIdStr);
          }

          if (qs.length === 0) {
            console.error('No valid questions returned');
            return;
          }

          setQuestions(qs);
          setCurrentQuestion(qs[0]);
          setQuestionIndex(0);
          setQuestionAnswered(false); // Reset for first question
          setAnswerResult(null); // Reset answer result
          startTimer(qs[0].time_limit || 30);
        } catch (err) {
          console.log('Error fetching questions:', err);
        }
      };

      // Check if we have 2 participants or game is active and should start
      if ((participants.length === 2 || game?.status === 'active') && waiting) {
        console.log('Starting game - participants:', participants.length, 'status:', game?.status);
        setWaiting(false);
        fetchQuestions();
      }
    }, [participants, game?.status, waiting]);

    // Handle timer timeout
    useEffect(() => {
      if (timeRemaining === 0 && currentQuestion && user && !waiting && !questionAnswered) {
        handleTimeout();
      }
    }, [timeRemaining, currentQuestion, user, waiting, questionAnswered]);

    const handleTimeout = async () => {
      if (!currentQuestion || !user || questionAnswered) return;

      console.log('Time up! Auto-submitting failed answer');
      const timeLimit = currentQuestion.time_limit || 30;

      setQuestionAnswered(true); // Prevent double submission
      setAnswerResult('wrong'); // Time up means wrong answer

      // Submit an empty/wrong answer to mark as failed
      await submitAnswer(user.id, currentQuestion.id, '', timeLimit);

      // Wait 1.5 seconds before moving to next question
      setTimeout(() => {
        moveToNextQuestion();
      }, 1500);
    };
  
    const handleAnswer = async (optionLabel: string) => {
      if (!currentQuestion || !user || questionAnswered) return;

      setQuestionAnswered(true); // Prevent timeout from firing

      // Check if the answer is correct
      const isCorrect = optionLabel.toLowerCase().trim() === currentQuestion.correct_answer.toLowerCase().trim();
      setAnswerResult(isCorrect ? 'correct' : 'wrong');

      const timeLimit = currentQuestion.time_limit || 30;
      const timeTaken = timeLimit - timeRemaining;

      await submitAnswer(user.id, currentQuestion.id, optionLabel, timeTaken);

      // Wait 1.5 seconds before moving to next question
      setTimeout(() => {
        moveToNextQuestion();
      }, 1500);
    };

    const moveToNextQuestion = async () => {
      if (questionIndex + 1 < questions.length) {
        const nextQuestion = questions[questionIndex + 1];
        setQuestionIndex(questionIndex + 1);
        setCurrentQuestion(nextQuestion);
        setQuestionAnswered(false); // Reset for next question
        setAnswerResult(null); // Reset answer result
        startTimer(nextQuestion.time_limit || 30);
      } else {
        // Current user finished all questions
        console.log('User finished all questions, checking game completion...');

        // Check if the entire game is complete (all players finished)
        if (gameId) {
          const isGameComplete = await GameService.checkGameCompletion(gameId, questions.length);
          if (isGameComplete) {
            // Mark game as finished
            await GameService.endGame(gameId);
          }
        }

        // Show results screen
        setShowResults(true);
      }
    };

    const handleLeaveGame = async () => {
      if (!user || !gameId) return;

      try {
        await leaveGame(user.id);
        // Navigate back to home
        router.push("(tabs)/home");
      } catch (err) {
        console.error('Error leaving game:', err);
      }
    };

    const handleRefreshGame = async () => {
      setRefreshing(true);
      console.log('Manually refreshing game state...');
      try {
        await refreshGameData();
      } finally {
        setRefreshing(false);
      }
    };
  
    // Show results screen if game is finished
    if (showResults && gameId && user) {
      return (
        <GameResults
          gameId={gameId}
          currentUserId={user.id}
          totalQuestions={questions.length}
          onClose={() => setShowResults(false)}
        />
      );
    }

    // Early returns for loading states - moved after all hooks
    if (!user) {
      return <SafeAreaView>
        <Text>Loading user...</Text>
      </SafeAreaView>;
    }

    if (loading || waiting) {
      return <SafeAreaView className="flex-1 p-4">
        <View className="flex-1 justify-center items-center">
          <Text className="text-xl font-semibold mb-4">Looking for an opponent...</Text>
          <Text className="text-gray-600 mb-2">Participants: {participants.length}/2</Text>
          <Text className="text-gray-600 mb-2">Game Status: {game?.status || 'unknown'}</Text>
          <Text className="text-gray-600 mb-2">Game ID: {gameId || 'none'}</Text>
          <Text className="text-sm text-gray-500 text-center mb-6">
            Tap refresh if someone joined but the game hasn't started
          </Text>

          {/* Refresh Button */}
          <TouchableOpacity
            onPress={handleRefreshGame}
            disabled={refreshing}
            className={`px-6 py-3 rounded-lg mb-4 ${
              refreshing ? 'bg-blue-300' : 'bg-blue-500'
            }`}
          >
            <Text className="text-white font-semibold">
              {refreshing ? '🔄 Refreshing...' : '🔄 Refresh'}
            </Text>
          </TouchableOpacity>

          {/* Leave Game Button */}
          <TouchableOpacity
            onPress={handleLeaveGame}
            className="bg-red-500 px-6 py-3 rounded-lg"
          >
            <Text className="text-white font-semibold">Leave Game</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>;
    }

    if (error) {
      return <SafeAreaView>
        <Text>Error: {error}</Text>
      </SafeAreaView>;
    }

    if (!currentQuestion) {
      return <SafeAreaView>
        <Text>Loading question...</Text>
      </SafeAreaView>;
    }
  
    return (
      <SafeAreaView className="flex-1 p-4">
        {/* Header with timer and leave button */}
        <View className="flex-row justify-between items-center mb-4">
          <Text className={`text-lg font-bold ${
            timeRemaining <= 5 ? 'text-red-500' :
            timeRemaining <= 10 ? 'text-orange-500' : 'text-black'
          }`}>
            Time left: {timeRemaining}s
          </Text>
          <TouchableOpacity
            onPress={handleLeaveGame}
            className="bg-red-500 px-4 py-2 rounded-lg"
          >
            <Text className="text-white font-semibold">Leave</Text>
          </TouchableOpacity>
        </View>

        <Text className="text-xl font-semibold mb-4 text-center">
          {currentQuestion.question_text}
        </Text>

        {questionAnswered && answerResult && (
          <Text className={`text-center font-semibold mb-4 text-lg ${
            answerResult === 'correct' ? 'text-green-600' : 'text-red-600'
          }`}>
            {answerResult === 'correct' ? '✅ You are correct!' : '❌ Sorry, that answer was wrong'}
          </Text>
        )}
        {timeRemaining === 0 && !questionAnswered && (
          <Text className="text-center text-red-600 font-semibold mb-4">
            Time's up! Moving to next question...
          </Text>
        )}

        <View className="space-y-3">
          {currentQuestion.options?.map((option: any) => (
            <TouchableOpacity
              key={option.label}
              onPress={() => handleAnswer(option.label)}
              disabled={questionAnswered}
              className={`p-4 rounded-lg border mb-3 ${
                questionAnswered
                  ? 'bg-gray-200 border-gray-300'
                  : 'bg-blue-100 border-blue-200'
              }`}
            >
              <Text className={`text-lg ${questionAnswered ? 'text-gray-500' : 'text-black'}`}>
                {option.label}. {option.text}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <View className="mt-6">
          <Text className="text-sm text-gray-600">
            Question {questionIndex + 1} of {questions.length}
          </Text>
          <Text className="text-sm text-gray-600">
            Players: {participants.length}/2
          </Text>
        </View>
      </SafeAreaView>
    );
  };

export default duoGameScreen;