import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams } from 'expo-router';
import { useEffect, useState } from 'react';
import { Image, RefreshControl, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { supabase } from '../../../lib/supabase';

interface LeaderboardUser {
  id: string;
  display_name: string;
  avatar_url: string | null;
  level: number;
  experience_points: number;
  period_score: number;
  games_played: number;
  games_won: number;
  created_at: string;
  rank: number;
}

type TimeFrame = 'all_time' | 'this_month' | 'this_week' | 'today';

const Leaderboard = () => {

  const params = useLocalSearchParams<{ timeFrame?: TimeFrame }>();
  const initialTimeFrame = (params.timeFrame as TimeFrame) || 'all_time';

  const [users, setUsers] = useState<LeaderboardUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTimeFrame, setSelectedTimeFrame] = useState<TimeFrame>(initialTimeFrame);
  const [error, setError] = useState<string | null>(null);

  const timeFrameOptions = [
    { key: 'all_time', label: 'All Time' },
    { key: 'this_month', label: 'This Month' },
    { key: 'this_week', label: 'This Week' },
    { key: 'today', label: 'Today' },
  ];

  const loadLeaderboard = async (timeFrame: TimeFrame = selectedTimeFrame) => {
    try {
      setError(null);

      if (timeFrame === 'all_time') {
        // For all-time, use total_score from users table
        const { data, error } = await supabase
          .from('users')
          .select('id, display_name, avatar_url, level, experience_points, total_score, games_played, games_won, created_at')
          .order('total_score', { ascending: false })
          .order('experience_points', { ascending: false })
          .limit(100);

        if (error) throw error;

        const rankedUsers: LeaderboardUser[] = (data || []).map((user, index) => ({
          ...user,
          period_score: user.total_score,
          rank: index + 1,
        }));

        setUsers(rankedUsers);
      } else {
        // For period-specific leaderboards, get scores from game_answers
        const now = new Date();
        let startDate: Date;

        switch (timeFrame) {
          case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            break;
          case 'this_week':
            const dayOfWeek = now.getDay();
            // Convert Sunday (0) to 7, so Monday becomes 1, Tuesday becomes 2, etc.
            const mondayBasedDay = dayOfWeek === 0 ? 7 : dayOfWeek;
            // Calculate days since Monday (Monday = 1, so subtract 1)
            const daysSinceMonday = mondayBasedDay - 1;
            startDate = new Date(now.getTime() - daysSinceMonday * 24 * 60 * 60 * 1000);
            startDate.setHours(0, 0, 0, 0);
            break;
          case 'this_month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
          default:
            startDate = new Date(0);
        }

        // Get period-specific scores by summing points from game_answers
        const { data, error } = await supabase
          .from('game_answers')
          .select(`
            user_id,
            points_earned,
            users!inner(id, display_name, avatar_url, level, experience_points, games_played, games_won, created_at)
          `)
          .gte('answered_at', startDate.toISOString());

        if (error) throw error;

        // Group by user and sum their period scores
        const userScores = new Map<string, LeaderboardUser>();

        (data || []).forEach((answer: any) => {
          const userId = answer.user_id;
          const user = answer.users;

          if (userScores.has(userId)) {
            const existing = userScores.get(userId)!;
            existing.period_score += answer.points_earned;
          } else {
            userScores.set(userId, {
              id: user.id,
              display_name: user.display_name,
              avatar_url: user.avatar_url,
              level: user.level,
              experience_points: user.experience_points,
              period_score: answer.points_earned,
              games_played: user.games_played,
              games_won: user.games_won,
              created_at: user.created_at,
              rank: 0, // Will be set below
            });
          }
        });

        // Convert to array, sort by period score, and add ranks
        const rankedUsers: LeaderboardUser[] = Array.from(userScores.values())
          .sort((a, b) => b.period_score - a.period_score)
          .slice(0, 100)
          .map((user, index) => ({
            ...user,
            rank: index + 1,
          }));

        setUsers(rankedUsers);
      }
    } catch (err) {
      console.error('Error loading leaderboard:', err);
      setError((err as Error).message);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadLeaderboard();
    setRefreshing(false);
  };

  const handleTimeFrameChange = (timeFrame: TimeFrame) => {
    setSelectedTimeFrame(timeFrame);
    setLoading(true);
    loadLeaderboard(timeFrame).finally(() => setLoading(false));
  };

  useEffect(() => {
    loadLeaderboard().finally(() => setLoading(false));
  }, []);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return `#${rank}`;
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return '#FFD700'; // Gold
      case 2:
        return '#C0C0C0'; // Silver
      case 3:
        return '#CD7F32'; // Bronze
      default:
        return '#6B7280'; // Gray
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="p-4 border-b border-gray-200">
        <Text className="text-2xl font-bold text-center mb-4">🏆 Leaderboard</Text>

        {/* Time Frame Selector */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} className="mb-2">
          <View className="flex-row">
            {timeFrameOptions.map((option) => (
              <TouchableOpacity
                key={option.key}
                onPress={() => handleTimeFrameChange(option.key as TimeFrame)}
                className={`px-4 py-2 rounded-full mr-2 ${
                  selectedTimeFrame === option.key
                    ? 'bg-blue-500'
                    : 'bg-gray-200'
                }`}
              >
                <Text
                  className={`font-medium ${
                    selectedTimeFrame === option.key
                      ? 'text-white'
                      : 'text-gray-700'
                  }`}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {loading ? (
          <View className="flex-1 justify-center items-center p-8">
            <Text className="text-lg text-gray-600">Loading leaderboard...</Text>
          </View>
        ) : error ? (
          <View className="flex-1 justify-center items-center p-8">
            <Ionicons name="alert-circle" size={64} color="#EF4444" />
            <Text className="text-red-500 text-center mt-4 text-lg">
              Error loading leaderboard
            </Text>
            <Text className="text-gray-600 text-center mt-2">{error}</Text>
            <TouchableOpacity
              onPress={onRefresh}
              className="bg-blue-500 px-6 py-3 rounded-lg mt-4"
            >
              <Text className="text-white font-semibold">Try Again</Text>
            </TouchableOpacity>
          </View>
        ) : users.length === 0 ? (
          <View className="flex-1 justify-center items-center p-8">
            <Ionicons name="trophy-outline" size={64} color="#9CA3AF" />
            <Text className="text-gray-500 text-center mt-4 text-lg">
              No players found
            </Text>
            <Text className="text-gray-400 text-center mt-2">
              Be the first to play and claim the top spot!
            </Text>
          </View>
        ) : (
          <View className="p-4">
            {users.map((user) => (
              <View
                key={user.id}
                className={`flex-row items-center p-4 mb-3 rounded-lg ${
                  user.rank <= 3 ? 'bg-gradient-to-r from-yellow-50 to-orange-50' : 'bg-gray-50'
                }`}
                style={{
                  borderLeftWidth: 4,
                  borderLeftColor: getRankColor(user.rank),
                }}
              >
                {/* Rank */}
                <View className="w-12 items-center">
                  <Text
                    className="text-lg font-bold"
                    style={{ color: getRankColor(user.rank) }}
                  >
                    {getRankIcon(user.rank)}
                  </Text>
                </View>

                {/* Avatar */}
                <View className="mr-3">
                  {user.avatar_url ? (
                    <Image
                      source={{ uri: user.avatar_url }}
                      className="w-12 h-12 rounded-full"
                    />
                  ) : (
                    <View className="w-12 h-12 rounded-full bg-gray-300 justify-center items-center">
                      <Ionicons name="person" size={24} color="#6B7280" />
                    </View>
                  )}
                </View>

                {/* User Info */}
                <View className="flex-1">
                  <Text className="font-semibold text-gray-900" numberOfLines={1}>
                    {user.display_name}
                  </Text>
                  <View className="flex-row items-center mt-1">
                    <Text className="text-sm text-gray-600 mr-3">
                      Level {user.level}
                    </Text>
                    <Text className="text-sm text-gray-600">
                      {user.games_played} games
                    </Text>
                  </View>
                </View>

                {/* Score */}
                <View className="items-end">
                  <Text className="text-lg font-bold text-blue-600">
                    {user.period_score.toLocaleString()}
                  </Text>
                  <Text className="text-sm text-gray-500">
                    {user.experience_points} XP
                  </Text>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default Leaderboard;