import { supabase } from '../lib/supabase';

export class LeaderboardService {
    static async getGlobalLeaderboard(
      periodType: 'daily' | 'weekly' | 'monthly' | 'all_time' = 'all_time',
      categoryId?: string,
      limit = 100
    ) {
      let query = supabase
        .from('users')
        .select('id, username, display_name, avatar_url, total_score, games_played, games_won, level')
        .order('total_score', { ascending: false })
        .limit(limit);
  
      // For period-specific leaderboards, you'd join with the leaderboards table
      if (periodType !== 'all_time') {
        const periodStart = this.getPeriodStart(periodType);
        query = supabase
          .from('leaderboards')
          .select(`
            *,
            user:users(id, username, display_name, avatar_url, level)
          `)
          .eq('period_type', periodType)
          .eq('period_start', periodStart)
          .order('total_score', { ascending: false })
          .limit(limit);
  
        if (categoryId) {
          query = query.eq('category_id', categoryId);
        }
      }
  
      const { data, error } = await query;
      if (error) throw error;
      return data || [];
    }
  
    static async getUserRank(userId: string, periodType = 'all_time', categoryId?: string): Promise<number> {
      // This would require a more complex query or stored procedure
      // For now, simplified implementation
      const { data, error } = await supabase.rpc('get_user_rank', {
        p_user_id: userId,
        p_period_type: periodType,
        p_category_id: categoryId,
      });
  
      if (error) throw error;
      return data || 0;
    }
  
    private static getPeriodStart(periodType: string): string {
      const now = new Date();
      switch (periodType) {
        case 'daily':
          return now.toISOString().split('T')[0];
        case 'weekly':
          const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
          return startOfWeek.toISOString().split('T')[0];
        case 'monthly':
          return new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
        default:
          return '1970-01-01';
      }
    }
  }
  