import { useEffect } from 'react';
import { supabase } from '../lib/supabase';

interface UseRealtimeSubscriptionProps {
  table: string;
  filter?: string;
  event?: '*' | 'INSERT' | 'UPDATE' | 'DELETE';
  onUpdate?: (payload: { type: string; event: string; payload: any }) => void;
}

export const useRealtimeSubscription = ({
  table,
  filter,
  event = '*',
  onUpdate,
}: UseRealtimeSubscriptionProps) => {
  useEffect(() => {
    const channel = supabase
      .channel(`realtime_${table}_${Date.now()}`)
      .on('postgres_changes' as any, {
        event,
        schema: 'public',
        table,
        ...(filter && { filter }),
      }, (payload) => {
        onUpdate?.(payload);
      })
      .subscribe();

    return () => {
      channel.unsubscribe();
    };
  }, [table, filter, event, onUpdate]);
};