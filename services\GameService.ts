import { supabase } from '../lib/supabase';
import { UserProfile } from '../types';
import { QuizService } from './QuizService';

interface GameSession {
  id: string;
  quiz_id: string;
  max_players: number;
  current_players: number;
  game_mode: string;
  is_private: boolean;
  invite_code?: string;
  status: 'waiting' | 'active' | 'paused' | 'finished' | 'cancelled';
  current_question_index: number;
  question_start_time?: string;
  settings: any;
  created_at: string;
  started_at?: string;
  finished_at?: string;
}

interface GameParticipant {
  id: string;
  game_id: string;
  user_id: string;
  user?: UserProfile;
  is_ready: boolean;
  is_connected: boolean;
  current_score: number;
  questions_answered: number;
  correct_answers: number;
  current_streak: number;
  joined_at: string;
}

interface GameAnswer {
  id: string;
  game_id: string;
  user_id: string;
  question_id: string;
  submitted_answer: string;
  is_correct: boolean;
  points_earned: number;
  time_taken: number;
  answered_at: string;
}

export class GameService {
  static async getGameSession(gameId: string): Promise<GameSession> {
    const { data, error } = await supabase
      .from('game_sessions')
      .select('*')
      .eq('id', gameId)
      .single();

    if (error) throw error;
    return data;
  }

  static async findOrCreateGame(quizId: string, gameMode = 'battle', userId?: string): Promise<GameSession> {
    console.log('🔍 Finding/creating game for:', { quizId, gameMode, userId });

    // For random sessions, try to find existing waiting games first
    if (QuizService.isRandomSession(quizId)) {
      console.log('🎲 This is a random session, looking for existing games...');

      // Try to find an existing waiting game for this random session type
      // First get all waiting games with null quiz_id, then filter in JavaScript
      const { data: allWaitingGames, error: findError } = await supabase
        .from('game_sessions')
        .select('*')
        .eq('status', 'waiting')
        .eq('game_mode', gameMode)
        .is('quiz_id', null)
        .lt('current_players', 2);

      // Filter games that match our random session ID
      const existingGames = allWaitingGames?.filter(game =>
        game.settings?.random_session_id === quizId
      ) || [];

      console.log('🔍 Search results:', {
        allWaitingGames: allWaitingGames?.length || 0,
        filteredGames: existingGames.length,
        findError
      });

      if (findError) {
        console.error('❌ Error finding random games:', findError);
      } else if (existingGames && existingGames.length > 0) {
        console.log(`✅ Found ${existingGames.length} existing games, checking user participation...`);

        // Check if user is not already in any of these games
        for (const game of existingGames) {
          console.log(`🎮 Checking game ${game.id} for user ${userId}...`);

          const { data: participants } = await supabase
            .from('game_participants')
            .select('user_id')
            .eq('game_id', game.id)
            .eq('user_id', userId);

          console.log(`👥 Participants check for game ${game.id}:`, participants);

          if (!participants || participants.length === 0) {
            console.log(`🎯 Found suitable game: ${game.id}`);
            return game; // Found a game the user is not in
          } else {
            console.log(`⚠️ User already in game ${game.id}, skipping...`);
          }
        }
        console.log('❌ User is already in all available games');
      } else {
        console.log('📝 No existing games found, will create new one');
      }

      // If no existing game found, create a new one
      console.log('🆕 Creating new random game session...');
      const gameData = {
        quiz_id: null, // Set to null for random sessions
        max_players: 2,
        current_players: 0,
        game_mode: gameMode,
        is_private: false,
        status: 'waiting',
        current_question_index: 0,
        settings: {
          show_correct_answer: true,
          allow_powerups: false,
          random_session_id: quizId, // Store the random session ID in settings
        },
      };

      console.log('📝 Game data to insert:', gameData);

      const { data: newGame, error: createError } = await supabase
        .from('game_sessions')
        .insert(gameData)
        .select()
        .single();

      if (createError) {
        console.error('❌ Error creating game:', createError);
        throw createError;
      }

      console.log('✅ Created new game:', newGame);
      return newGame;
    }

    // For regular quizzes, try to find an existing waiting game
    const { data: existingGames, error: findError } = await supabase
      .rpc('find_available_game', {
        p_quiz_id: quizId,
        p_game_mode: gameMode,
        p_user_id: userId || null
      });

    if (findError) throw findError;

    if (existingGames && existingGames.length > 0) {
      return existingGames[0];
    }

    // Create new game if none found
    const { data: newGame, error: createError } = await supabase
      .from('game_sessions')
      .insert({
        quiz_id: quizId,
        max_players: 2,
        current_players: 0,
        game_mode: gameMode,
        is_private: false,
        status: 'waiting',
        current_question_index: 0,
        settings: {
          show_correct_answer: true,
          allow_powerups: false,
        },
      })
      .select()
      .single();

    if (createError) throw createError;
    return newGame;
  }

  static async joinGame(gameId: string, userId: string): Promise<GameParticipant> {
    // Check if user is already in this game
    const { data: existingParticipant } = await supabase
      .from('game_participants')
      .select('*')
      .eq('game_id', gameId)
      .eq('user_id', userId)
      .single();

    if (existingParticipant) {
      // User is already in the game, return existing participant
      const { data: participant } = await supabase
        .from('game_participants')
        .select(`
          *,
          user:users(*)
        `)
        .eq('id', existingParticipant.id)
        .single();

      return participant;
    }

    const { data: participant, error } = await supabase
      .from('game_participants')
      .insert({
        game_id: gameId,
        user_id: userId,
        is_ready: false,
        is_connected: true,
        current_score: 0,
        questions_answered: 0,
        correct_answers: 0,
        current_streak: 0,
      })
      .select(`
        *,
        user:users(*)
      `)
      .single();

    if (error) throw error;

    // Only increment for new participants
    // Update game's current_players count and start game if full
    await supabase.rpc('increment_game_players_and_start', { game_id: gameId });

    return participant;
  }

  static async leaveGame(gameId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('game_participants')
      .delete()
      .eq('game_id', gameId)
      .eq('user_id', userId);

    if (error) throw error;

    // Decrement game's current_players count and update status
    await supabase.rpc('decrement_game_players_and_update_status', { game_id: gameId });
  }

  static async setPlayerReady(gameId: string, userId: string, isReady: boolean): Promise<void> {
    const { error } = await supabase
      .from('game_participants')
      .update({ is_ready: isReady })
      .eq('game_id', gameId)
      .eq('user_id', userId);

    if (error) throw error;
  }

  static async submitAnswer(
    gameId: string,
    userId: string,
    questionId: string,
    answer: string,
    timeTaken: number
  ): Promise<GameAnswer> {
    // Get the correct answer to validate
    const { data: question, error: questionError } = await supabase
      .from('questions')
      .select('correct_answer, base_points')
      .eq('id', questionId)
      .single();

    if (questionError) throw questionError;

    const isCorrect = answer.toLowerCase().trim() === question.correct_answer.toLowerCase().trim();
    const pointsEarned = isCorrect ? this.calculatePoints(question.base_points, timeTaken) : 0;

    // Insert the answer
    const { data: gameAnswer, error } = await supabase
      .from('game_answers')
      .insert({
        game_id: gameId,
        user_id: userId,
        question_id: questionId,
        submitted_answer: answer,
        is_correct: isCorrect,
        points_earned: pointsEarned,
        time_taken: timeTaken,
      })
      .select()
      .single();

    if (error) throw error;

    // Update participant's score and stats
    await supabase.rpc('update_participant_score', {
      p_game_id: gameId,
      p_user_id: userId,
      p_points: pointsEarned,
      p_is_correct: isCorrect,
    });

    return gameAnswer;
  }

  static calculatePoints(basePoints: number, timeTaken: number, timeLimit = 30): number {
    // Award bonus points for speed (up to 50% bonus)
    const timeBonus = Math.max(0, (timeLimit - timeTaken) / timeLimit * 0.5);
    return Math.round(basePoints * (1 + timeBonus));
  }

  static calculateAccuracyRate(correctAnswers: number, totalQuestions: number): number {
    if (totalQuestions === 0) return 0;
    return Math.round((correctAnswers / totalQuestions) * 100 * 100) / 100; // Round to 2 decimal places
  }

  static async getGameParticipants(gameId: string): Promise<GameParticipant[]> {
    const { data, error } = await supabase
      .from('game_participants')
      .select(`
        *,
        user:users(id, username, display_name, avatar_url, level)
      `)
      .eq('game_id', gameId)
      .order('joined_at', { ascending: true });

    if (error) throw error;
    return data || [];
  }

  static async getGameResults(gameId: string): Promise<any> {
    const { data, error } = await supabase
      .from('game_participants')
      .select(`
        *,
        user:users(id, username, display_name, avatar_url, level, experience_points)
      `)
      .eq('game_id', gameId)
      .order('current_score', { ascending: false });

    if (error) throw error;
    return data;
  }

  static async endGame(gameId: string): Promise<void> {
    try {
      // Update game status to finished
      await supabase
        .from('game_sessions')
        .update({
          status: 'finished',
          finished_at: new Date().toISOString()
        })
        .eq('id', gameId);

      // Update user statistics for all participants
      await this.updateAllParticipantStats(gameId);
    } catch (error) {
      console.error('Error ending game:', error);
      throw error;
    }
  }

  static async updateAllParticipantStats(gameId: string): Promise<void> {
    try {
      const { data: participants, error: participantsError } = await supabase
        .from('game_participants')
        .select(`
          user_id,
          current_score,
          questions_answered,
          correct_answers,
          current_streak,
          user:users(
            id,
            games_played,
            games_won,
            current_streak,
            best_streak,
            total_questions_answered,
            correct_answers,
            total_score
          )
        `)
        .eq('game_id', gameId);

      if (participantsError) throw participantsError;
      if (!participants || participants.length === 0) return;

      // Determine winner(s)
      const maxScore = Math.max(...participants.map(p => p.current_score));
      const winners = participants.filter(p => p.current_score === maxScore);
      const isWin = (participant: any) => winners.some(w => w.user_id === participant.user_id);
      const isTie = winners.length > 1;

      // Update each participant's stats
      for (const participant of participants) {
        await this.updateUserGameStats(
          participant.user_id,
          participant,
          isWin(participant),
          isTie
        );
      }
    } catch (error) {
      console.error('Error updating participant stats:', error);
      throw error;
    }
  }

  static async updateUserGameStats(
    userId: string,
    gameData: any,
    won: boolean,
    tie: boolean
  ): Promise<void> {
    try {
      const user = gameData.user;
      if (!user) return;

      // Calculate new overall accuracy
      const newTotalQuestions = user.total_questions_answered + gameData.questions_answered;
      const newTotalCorrect = user.correct_answers + gameData.correct_answers;
      const newAccuracyRate = newTotalQuestions > 0
        ? (newTotalCorrect / newTotalQuestions) * 100
        : 0;

      // Handle streak logic
      let newCurrentStreak = user.current_streak;
      let newBestStreak = user.best_streak;

      if (won && !tie) {
        // Win increases streak
        newCurrentStreak = user.current_streak + 1;
        newBestStreak = Math.max(newBestStreak, newCurrentStreak);
      } else if (!won && !tie) {
        // Loss resets streak
        newCurrentStreak = 0;
      }
      // Tie maintains current streak

      // Update user stats
      const { error } = await supabase
        .from('users')
        .update({
          games_played: user.games_played + 1,
          games_won: won ? user.games_won + 1 : user.games_won,
          current_streak: newCurrentStreak,
          best_streak: newBestStreak,
          total_questions_answered: newTotalQuestions,
          correct_answers: newTotalCorrect,
          total_score: user.total_score + gameData.current_score,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) throw error;

      console.log(`Updated stats for user ${userId}:`, {
        games_played: user.games_played + 1,
        games_won: won ? user.games_won + 1 : user.games_won,
        current_streak: newCurrentStreak,
        best_streak: newBestStreak,
        total_questions_answered: newTotalQuestions,
        correct_answers: newTotalCorrect,
        accuracy_rate: newAccuracyRate.toFixed(2) + '%',
        total_score: user.total_score + gameData.current_score
      });

    } catch (error) {
      console.error(`Error updating stats for user ${userId}:`, error);
      throw error;
    }
  }

  static async checkGameCompletion(gameId: string, totalQuestions: number): Promise<boolean> {
    try {
      // Check if all participants have answered all questions
      const { data: participants } = await supabase
        .from('game_participants')
        .select('questions_answered')
        .eq('game_id', gameId);

      if (!participants) return false;

      // Game is complete if all participants have answered all questions
      return participants.every(p => p.questions_answered >= totalQuestions);
    } catch (error) {
      console.error('Error checking game completion:', error);
      return false;
    }
  }
}