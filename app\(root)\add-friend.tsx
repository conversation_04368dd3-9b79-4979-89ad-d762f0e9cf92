import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '@/hooks/useAuth';
import { FriendService } from '@/services/FriendService';
import { useState } from 'react';
import {
  Alert,
  Image,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  FlatList,
  ActivityIndicator
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { UserProfile } from '@/types';
import { router } from 'expo-router';

const AddFriend = () => {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(false);
  const [sendingRequest, setSendingRequest] = useState<string | null>(null);

  const handleSearch = async () => {
    if (!searchQuery.trim() || !user) return;

    setLoading(true);
    try {
      const results = await FriendService.searchUsers(searchQuery.trim(), user.id);
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching users:', error);
      Alert.alert('Error', 'Failed to search users');
    } finally {
      setLoading(false);
    }
  };

  const handleSendFriendRequest = async (addresseeId: string) => {
    if (!user) return;

    setSendingRequest(addresseeId);
    try {
      await FriendService.sendFriendRequest(user.id, addresseeId);
      Alert.alert('Success', 'Friend request sent!');
      
      // Remove the user from search results
      setSearchResults(prev => prev.filter(u => u.id !== addresseeId));
    } catch (error) {
      console.error('Error sending friend request:', error);
      Alert.alert('Error', 'Failed to send friend request');
    } finally {
      setSendingRequest(null);
    }
  };

  const renderUserItem = ({ item }: { item: UserProfile }) => {
    const isLoading = sendingRequest === item.id;

    return (
      <View className="flex-row items-center p-4 bg-white rounded-2xl mb-3 shadow-sm">
        {/* Avatar */}
        <View className="mr-3">
          {item.avatar_url ? (
            <Image
              source={{ uri: item.avatar_url }}
              className="w-12 h-12 rounded-full"
            />
          ) : (
            <View className="w-12 h-12 rounded-full bg-gray-300 justify-center items-center">
              <Ionicons name="person" size={24} color="#6B7280" />
            </View>
          )}
        </View>

        {/* User Info */}
        <View className="flex-1">
          <Text className="font-semibold text-gray-900" numberOfLines={1}>
            {item.display_name || item.username}
          </Text>
          <Text className="text-sm text-gray-500">@{item.username}</Text>
          <View className="flex-row items-center mt-1">
            <Text className="text-xs text-gray-600 mr-3">Level {item.level}</Text>
            <Text className="text-xs text-gray-600">{item.total_score} points</Text>
          </View>
        </View>

        {/* Add Friend Button */}
        <TouchableOpacity
          onPress={() => handleSendFriendRequest(item.id)}
          disabled={isLoading}
          className="bg-blue-500 rounded-full px-4 py-2 flex-row items-center"
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <>
              <Ionicons name="person-add" size={16} color="white" />
              <Text className="text-white font-medium ml-1">Add</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="bg-white px-6 py-4 border-b border-gray-200">
        <View className="flex-row items-center">
          <TouchableOpacity onPress={() => router.back()} className="mr-4">
            <Ionicons name="arrow-back" size={24} color="#374151" />
          </TouchableOpacity>
          <Text className="text-xl font-bold text-gray-900">Add Friends</Text>
        </View>
      </View>

      {/* Search Section */}
      <View className="p-6">
        <View className="flex-row items-center bg-white rounded-2xl p-4 shadow-sm">
          <Ionicons name="search" size={20} color="#9CA3AF" />
          <TextInput
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search by username or display name"
            className="flex-1 ml-3 text-base"
            onSubmitEditing={handleSearch}
            returnKeyType="search"
          />
          <TouchableOpacity
            onPress={handleSearch}
            disabled={loading || !searchQuery.trim()}
            className="bg-blue-500 rounded-full px-4 py-2 ml-2"
          >
            {loading ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text className="text-white font-medium">Search</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Results */}
      <View className="flex-1 px-6">
        {searchResults.length > 0 ? (
          <FlatList
            data={searchResults}
            renderItem={renderUserItem}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
          />
        ) : searchQuery && !loading ? (
          <View className="flex-1 justify-center items-center">
            <Ionicons name="search" size={48} color="#9CA3AF" />
            <Text className="text-gray-500 text-center mt-4">
              No users found matching "{searchQuery}"
            </Text>
            <Text className="text-gray-400 text-center text-sm mt-2">
              Try searching with a different username or display name
            </Text>
          </View>
        ) : !searchQuery ? (
          <View className="flex-1 justify-center items-center">
            <Ionicons name="people" size={48} color="#9CA3AF" />
            <Text className="text-gray-500 text-center mt-4">
              Search for users to add as friends
            </Text>
            <Text className="text-gray-400 text-center text-sm mt-2">
              Enter a username or display name to get started
            </Text>
          </View>
        ) : null}
      </View>
    </SafeAreaView>
  );
};

export default AddFriend;
