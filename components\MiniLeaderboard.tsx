import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import { supabase } from '../lib/supabase';

interface LeaderboardUser {
  id: string;
  display_name: string;
  avatar_url: string | null;
  level: number;
  experience_points: number;
  weekly_score: number;
  rank: number;
}

const MiniLeaderboard = () => {
  const [topUsers, setTopUsers] = useState<LeaderboardUser[]>([]);
  const [loading, setLoading] = useState(true);

  const loadTopUsers = async () => {
    try {
      // Get top 5 users for "this week" (Monday to current day)
      const now = new Date();
      const dayOfWeek = now.getDay();
      // Convert Sunday (0) to 7, so Monday becomes 1, Tuesday becomes 2, etc.
      const mondayBasedDay = dayOfWeek === 0 ? 7 : dayOfWeek;
      // Calculate days since Monday (Monday = 1, so subtract 1)
      const daysSinceMonday = mondayBasedDay - 1;
      const startOfWeek = new Date(now.getTime() - daysSinceMonday * 24 * 60 * 60 * 1000);
      startOfWeek.setHours(0, 0, 0, 0);

      // Get weekly scores by summing points from game_answers for this week
      const { data, error } = await supabase
        .from('game_answers')
        .select(`
          user_id,
          points_earned,
          users!inner(id, display_name, avatar_url, level, experience_points)
        `)
        .gte('answered_at', startOfWeek.toISOString());

      if (error) {
        throw error;
      }

      // Group by user and sum their weekly scores
      const userScores = new Map<string, LeaderboardUser>();

      (data || []).forEach((answer: any) => {
        const userId = answer.user_id;
        const user = answer.users;

        if (userScores.has(userId)) {
          const existing = userScores.get(userId)!;
          existing.weekly_score += answer.points_earned;
        } else {
          userScores.set(userId, {
            id: user.id,
            display_name: user.display_name,
            avatar_url: user.avatar_url,
            level: user.level,
            experience_points: user.experience_points,
            weekly_score: answer.points_earned,
            rank: 0, // Will be set below
          });
        }
      });

      // Convert to array, sort by weekly score, and add ranks
      const rankedUsers: LeaderboardUser[] = Array.from(userScores.values())
        .sort((a, b) => b.weekly_score - a.weekly_score)
        .slice(0, 5)
        .map((user, index) => ({
          ...user,
          rank: index + 1,
        }));

      setTopUsers(rankedUsers);
    } catch (err) {
      console.error('Error loading top users:', err);
      // Fallback to all-time top users if weekly filter fails
      try {
        const { data, error } = await supabase
          .from('users')
          .select('id, display_name, avatar_url, level, experience_points, total_score')
          .order('total_score', { ascending: false })
          .order('experience_points', { ascending: false })
          .limit(5);

        if (!error && data) {
          const rankedUsers: LeaderboardUser[] = data.map((user, index) => ({
            ...user,
            weekly_score: user.total_score, // Use total_score as fallback
            rank: index + 1,
          }));
          setTopUsers(rankedUsers);
        }
      } catch (fallbackErr) {
        console.error('Error loading fallback users:', fallbackErr);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTopUsers();
  }, []);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return `#${rank}`;
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return '#FFD700'; // Gold
      case 2:
        return '#C0C0C0'; // Silver
      case 3:
        return '#CD7F32'; // Bronze
      default:
        return '#6B7280'; // Gray
    }
  };

  if (loading) {
    return (
      <View className="bg-white rounded-2xl p-4 mx-4 mb-6 shadow-sm">
        <View className="flex-row items-center justify-between mb-4">
          <Text className="text-lg font-bold text-gray-900">🏆 This Week's Top Players</Text>
        </View>
        <View className="justify-center items-center py-8">
          <Text className="text-gray-500">Loading...</Text>
        </View>
      </View>
    );
  }

  if (topUsers.length === 0) {
    return (
      <View className="bg-white rounded-[32px] p-4 shadow-sm mt-2">
        <View className="flex-row items-center justify-between mb-4">
          <Text className="text-lg font-bold text-gray-900">🏆 This Week's Top Players</Text>
          <TouchableOpacity
            onPress={() => router.push('/(tabs)/leaderboard')}
            className="flex-row items-center"
          >
            <Text className="text-blue-500 font-medium mr-1">View All</Text>
            <Ionicons name="chevron-forward" size={16} color="#3B82F6" />
          </TouchableOpacity>
        </View>
        <View className="justify-center items-center py-8">
          <Ionicons name="trophy-outline" size={48} color="#9CA3AF" />
          <Text className="text-gray-500 text-center mt-2">
            No players this week
          </Text>
          <Text className="text-gray-400 text-center text-sm">
            Be the first to play!
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View className="bg-white rounded-[32px] p-4 shadow-sm mt-2">
      {/* Header */}
      <View className="flex-row items-center justify-between mb-4">
        <Text className="text-lg font-bold text-gray-900">🏆 This Week's Top Players</Text>
        <TouchableOpacity
          onPress={() => router.push({
            pathname: '/(tabs)/leaderboard',
            params: { timeFrame: 'this_week' }
          })}
          className="flex-row items-center"
        >
          <Text className="text-blue-500 font-medium mr-1">View All</Text>
          <Ionicons name="chevron-forward" size={16} color="#3B82F6" />
        </TouchableOpacity>
      </View>

      {/* Top Users List */}
      <View className="flex-row justify-between">
        {topUsers.map((user) => (
          <View
            key={user.id}
            className="flex-col items-center flex-1 mx-1"
          >
            {/* Rank */}
            {/* <View className="mb-2">
              <Text
                className="text-sm font-bold"
                style={{ color: getRankColor(user.rank) }}
              >
                {getRankIcon(user.rank)}
              </Text>
            </View> */}

            {/* Avatar */}
            <View className="mb-2">
              {user.avatar_url ? (
                <View className='relative'>
                  <Image
                    source={{ uri: user.avatar_url }}
                    className="w-14 h-14 rounded-full"
                  />
                  <View className="absolute -bottom-2 right-1/2 transform translate-x-1/2">
                    <Text
                      className="text-lg font-bold"
                      style={{ color: getRankColor(user.rank) }}
                    >
                      {getRankIcon(user.rank)}
                    </Text>
                  </View>
                </View>
                
              ) : (
                <View className="w-12 h-12 rounded-full bg-gray-300 justify-center items-center">
                  <Ionicons name="person" size={24} color="#6B7280" />
                </View>
              )}
            </View>

            {/* User Info */}
            <View className="items-center mb-2">
              <Text className="font-medium text-gray-900 text-center text-xs" numberOfLines={2}>
                {user.display_name}
              </Text>
              <Text className="text-xs text-gray-500">
                Level {user.level}
              </Text>
            </View>

            {/* Score */}
            {/* <View className="items-center">
              <Text className="text-sm font-bold text-blue-600">
                {user.weekly_score.toLocaleString()}
              </Text>
              <Text className="text-xs text-gray-500">
                {user.experience_points} XP
              </Text>
            </View> */}
          </View>
        ))}
      </View>

      {/* Footer */}
      {/* <TouchableOpacity
        onPress={() => router.push('/(tabs)/leaderboard')}
        className="mt-4 pt-3 border-t border-gray-100"
      >
        <Text className="text-center text-blue-500 font-medium">
          View Full Leaderboard
        </Text>
      </TouchableOpacity> */}
    </View>
  );
};

export default MiniLeaderboard;
