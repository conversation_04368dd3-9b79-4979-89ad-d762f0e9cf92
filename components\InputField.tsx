import {
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  TextInputProps,
  TouchableWithoutFeedback,
  View
} from "react-native";
  
interface InputFieldProps extends TextInputProps {
    image: any;
    placeholder: string;
}
  
const InputField = ({ image, placeholder, ...props }: InputFieldProps) => {
    return (
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View className='flex flex-row items-center gap-2 px-6 py-3 bg-white rounded-full'>
                <Image source={image} className='w-7 h-7' />
                <TextInput 
                    placeholder={placeholder} 
                    className='flex-1 text-[##1d1d1d] font-HelveticaNeueMedium'
                    {...props}
                />
            </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    );
};
  
export default InputField;