# CHAPTER TWO
# LITERATURE REVIEW

## 2.1 Introduction

The main aim of this chapter is to review existing interactive quiz systems and educational applications available online. This chapter will look into three existing systems concerning the development of an Online Quiz System web portal in A.B.U Zaria buses.

## 2.2 Kahoot Interactive Quiz Platform

Kahoot is a web-based platform that serves as a game-based learning platform primarily used in educational settings. According to (<PERSON> & <PERSON>, 2013), <PERSON><PERSON> provides a gamified approach to learning where educators can create interactive quizzes, discussions, and surveys that students can participate in using their mobile devices. The platform allows real-time participation where multiple students can join a quiz session simultaneously using a unique game PIN.

The system operates on a host-participant model where the educator acts as the host and controls the quiz flow while students join as participants. <PERSON><PERSON>'s interface displays questions on a shared screen while participants select answers on their individual devices. The platform incorporates competitive elements such as points, leaderboards, and time-based scoring to enhance student engagement (<PERSON><PERSON>, 2015).

However, Kahoot has several limitations that affect its educational effectiveness:

a. Limited question types - primarily supports multiple choice questions with minimal support for open-ended responses
b. Requires constant internet connectivity for both host and participants
c. <PERSON><PERSON> comprehensive content management for educational materials beyond quizzes
d. Does not provide offline access capabilities for students in areas with poor connectivity
e. Limited customization options for educational institutions with specific branding requirements

## 2.3 Quizizz Learning Platform

Quizizz is an interactive learning platform that allows educators to create engaging quizzes and lessons for students. According to (<PERSON>, 2019), Quizizz differentiates itself from other quiz platforms by allowing students to work at their own pace rather than following a synchronized timeline. The platform supports various question types including multiple choice, fill-in-the-blank, and open-ended questions, providing more flexibility than traditional quiz systems.

The platform incorporates several engaging features such as memes, music, and customizable avatars to create a more entertaining learning experience. Students can access Quizizz through web browsers or mobile applications, making it accessible across different devices (Mei et al., 2018). The system provides detailed analytics and reports that help educators track student progress and identify areas requiring additional attention.

Despite its advantages, Quizizz has notable limitations:

a. Limited real-time multiplayer capabilities - students work individually rather than competing directly
b. Requires internet connectivity for all features, limiting accessibility in low-connectivity environments
c. Lacks comprehensive content management for educational materials beyond quiz content
d. Does not support offline quiz-taking capabilities for students
e. Limited integration with institutional learning management systems
f. Absence of friend systems and social learning features that enhance peer-to-peer engagement

## 2.4 Socrative Student Response System

Socrative is a cloud-based student response system that enables educators to create interactive quizzes, polls, and assessments for real-time classroom engagement. According to (Wash, 2014), Socrative provides immediate feedback capabilities that allow educators to gauge student understanding instantly and adjust their teaching accordingly. The platform supports various question formats including multiple choice, true/false, and short answer questions.

The system operates through a simple room-based approach where educators create virtual rooms that students can join using room numbers. Socrative provides real-time results and analytics that help educators identify learning gaps and track student progress over time (Awedh et al., 2014). The platform offers both web-based and mobile application access, ensuring compatibility across different devices and operating systems.

However, Socrative presents several limitations for comprehensive educational use:

a. Limited question complexity - primarily supports basic question types without advanced multimedia integration
b. Lacks comprehensive content management system for educational materials storage and organization
c. Does not provide offline capabilities for students in areas with connectivity issues
d. Absence of social learning features such as friend systems and peer competitions
e. Limited customization options for institutional branding and interface personalization
f. Does not support comprehensive leaderboard systems for long-term student engagement tracking

## 2.5 Comparison Between Existing Systems and Proposed Interactive Quiz System

After analyzing the existing interactive quiz systems, several common limitations have been identified that the proposed system aims to address. The comparative analysis reveals significant gaps in current platforms that affect their educational effectiveness and user engagement.

### 2.5.1 Real-Time Multiplayer Capabilities

While platforms like Kahoot offer some real-time features, they lack true peer-to-peer competitive multiplayer functionality. The proposed system addresses this limitation by implementing:

a. Direct player-to-player matchmaking systems that allow students to compete against specific opponents
b. Real-time synchronization that ensures all participants experience simultaneous question delivery and timing
c. Live leaderboard updates that provide immediate competitive feedback during quiz sessions
d. Friend systems that enable students to challenge classmates and track comparative performance over time

### 2.5.2 Comprehensive Content Management

Existing systems like Quizizz and Socrative focus primarily on quiz delivery without providing comprehensive educational content management. The proposed system enhances this by offering:

a. Integrated document storage and management for course materials including PDFs, images, and presentations
b. Categorized content organization that allows students to access related materials alongside quiz content
c. Offline content caching capabilities that enable students to access materials without constant internet connectivity
d. Multi-format document support including Word documents, PowerPoint presentations, and multimedia files
