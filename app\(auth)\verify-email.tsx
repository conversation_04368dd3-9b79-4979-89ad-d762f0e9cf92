import InputField from '@/components/InputField'
import { AuthService } from '@/services/AuthService'
import { router, useLocalSearchParams } from 'expo-router'
import React, { useEffect, useState } from 'react'
import { Alert, KeyboardAvoidingView, Platform, Text, TouchableOpacity, View } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'

const VerifyEmail = () => {
  const { email } = useLocalSearchParams<{ email: string }>()
  const [otp, setOtp] = useState('')
  const [loading, setLoading] = useState(false)
  const [resendLoading, setResendLoading] = useState(false)
  const [countdown, setCountdown] = useState(60)
  const [canResend, setCanResend] = useState(false)

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    } else {
      setCanResend(true)
    }
  }, [countdown])

  const handleVerifyOTP = async () => {
    if (!otp.trim()) {
      Alert.alert('Error', 'Please enter the verification code')
      return
    }

    if (otp.length !== 6) {
      Alert.alert('Error', 'Verification code must be 6 digits')
      return
    }

    try {
      setLoading(true)
      
      // Use AuthService instead of direct supabase call
      const data = await AuthService.verifyOTP(email!, otp, 'signup')

      if (data.user) {
        Alert.alert(
          'Success!',
          'Your email has been verified and profile created. You can now sign in.',
          [
            {
              text: 'OK',
              onPress: () => router.replace('/(auth)/signin')
            }
          ]
        )
      }
    } catch (error) {
      console.error('OTP verification error:', error)
      Alert.alert(
        'Error', 
        error instanceof Error ? error.message : 'Invalid verification code. Please try again.'
      )
    } finally {
      setLoading(false)
    }
  }

  const handleResendCode = async () => {
    try {
      setResendLoading(true)
      
      await AuthService.resendOTP(email!)

      Alert.alert('Success', 'A new verification code has been sent to your email')
      setCountdown(60)
      setCanResend(false)
      setOtp('')
    } catch (error) {
      console.error('Resend error:', error)
      Alert.alert('Error', 'Failed to resend verification code. Please try again.')
    } finally {
      setResendLoading(false)
    }
  }

  return (
    <SafeAreaView className='flex-1 bg-[#e0e2e4]'>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className='flex-1'
      >
        <View className='flex-1 p-4 justify-center'>
          {/* Header */}
          <View className='mb-8'>
            <Text className='text-3xl font-HelveticaNeueMedium text-[#1d1d1d] text-center mb-2'>
              Verify Your Email
            </Text>
            <Text className='text-[#666] font-HelveticaNeue text-center mb-4'>
              We've sent a 6-digit verification code to
            </Text>
            <Text className='text-[#1d1d1d] font-HelveticaNeueMedium text-center'>
              {email}
            </Text>
          </View>

          {/* OTP Input */}
          <View className='mb-6'>
            <InputField 
              image={require('../../assets/images/Profile.png')}
              placeholder='Enter 6-digit code'
              value={otp}
              onChangeText={setOtp}
              keyboardType="number-pad"
              maxLength={6}
              autoCapitalize="none"
              textAlign="center"
              style={{ fontSize: 24, letterSpacing: 8 }}
            />
          </View>

          {/* Verify Button */}
          <TouchableOpacity 
            className={`rounded-full mb-4 ${
              loading ? 'bg-gray-400' : 'bg-[#1d1d1d]'
            }`}
            onPress={handleVerifyOTP}
            disabled={loading}
            activeOpacity={0.8}
          >
            <Text className='text-white p-6 font-HelveticaNeueMedium text-center'>
              {loading ? 'Verifying...' : 'Verify Email'}
            </Text>
          </TouchableOpacity>

          {/* Resend Code */}
          <View className='items-center'>
            <Text className='text-[#666] font-HelveticaNeue text-center mb-2'>
              Didn't receive the code?
            </Text>
            
            {canResend ? (
              <TouchableOpacity 
                onPress={handleResendCode}
                disabled={resendLoading}
              >
                <Text className='text-[#1d1d1d] font-HelveticaNeueMedium underline'>
                  {resendLoading ? 'Sending...' : 'Resend Code'}
                </Text>
              </TouchableOpacity>
            ) : (
              <Text className='text-[#666] font-HelveticaNeue'>
                Resend code in {countdown}s
              </Text>
            )}
          </View>

          {/* Back to Sign In */}
          <View className='flex-row justify-center items-center gap-2 mt-8'>
            <Text className='text-[#1d1d1d] font-HelveticaNeueMedium'>
              Wrong email?
            </Text>
            <TouchableOpacity 
              onPress={() => router.back()}
              disabled={loading}
            >
              <Text className='text-[#1d1d1d] font-HelveticaNeueMedium underline'>
                Go Back
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

export default VerifyEmail