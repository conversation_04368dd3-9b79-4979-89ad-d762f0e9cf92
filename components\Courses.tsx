import { QuizService } from '@/services/QuizService';
import { QuizCategory } from '@/types';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { Image, ScrollView, Text, TouchableOpacity, View } from 'react-native';

const Courses = () => {

    const [categories, setCategories] = useState<QuizCategory[]>([]);
    useEffect(() => {
        const fetchCategories = async () => {
        try {
            const data = await QuizService.getCategories();
            setCategories(data);
        } catch (error) {
            console.error('Failed to fetch categories:', error);
        }
        };

        fetchCategories();
    }, []);

    return (
      <View className='mt-2'>
        <View className='flex-row items-center justify-between'>
            <Text className='text-black font-HelveticaNeueBold text-[20px]'>Courses</Text>
            <TouchableOpacity onPress={() => router.push('/category')} className='flex-row items-center'>
              <Text className='text-black font-HelveticaNeueMedium text-[14px]'>See all</Text>
              <Image source={require("../assets/images/RightIcon.png")} className='h-[25px] w-[25px]' tintColor="black" />
            </TouchableOpacity>
        </View>
        
        <ScrollView showsVerticalScrollIndicator={false} className='mt-2'>
          {categories.map((cat) => (
            <TouchableOpacity
              key={cat.id}
              // style={{ backgroundColor: cat.color_hex || '#10B981' }}
              className="p-2 flex-row items-center rounded-[32px] mb-2 h-[300px] bg-white"
              onPress={() => router.push({
                pathname: '/course/[categoryId]',
                params: {
                  categoryId: cat.id,
                  categoryName: cat.name,
                  categoryDescription: cat.description
                }
              })}
            >
              <Image source={require("../assets/images/3dillustrations.png")} resizeMode='cover' className='w-[150px] h-[280px] rounded-[28px]' />
              <View className='flex-1 p-2 ml-3 justify-between'>
                  <View className='mb-4'>
                    <Text className="text-[20px] font-HelveticaNeueBold">
                      {cat.name}
                    </Text>
                  </View>
                  <View className='flex-1'>
                    <Text className="text-gray-400 font-HelveticaNeueBold text-[20px]" numberOfLines={0}>{cat.description}</Text>
                  </View>
              </View>
              

            </TouchableOpacity>
          ))}
          <View className='h-[400px]' />
        </ScrollView>
      </View>
      );
}

export default Courses