import Courses from '@/components/Courses';
import MiniLeaderboard from '@/components/MiniLeaderboard';
import Play from '@/components/Play';
import { useAuth } from '@/hooks/useAuth';
import { AuthService } from '@/services/AuthService';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import { useState } from 'react';
import {
  Alert,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View
} from "react-native";
import { SafeAreaView } from 'react-native-safe-area-context';

const home = () => {
  const { profile, signOut, user, refreshProfile } = useAuth();
  const [uploading, setUploading] = useState(false);
  const changeIconColor = true;

  const handleLogout = async () => {
    try {
      await signOut();
      router.replace('/(auth)/signin');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleProfilePicturePress = async () => {
    try {
      // Request permission
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant permission to access your photos');
        return;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.5,
      });

      if (!result.canceled && result.assets[0].uri) {
        setUploading(true);
        try {
          if (!user) throw new Error('No user found');

          console.log('Selected image URI:', result.assets[0].uri);
          // Upload the image
          await AuthService.uploadProfilePicture(user.id, result.assets[0].uri);

          // Refresh the profile to get the new avatar URL
          await refreshProfile();

          Alert.alert('Success', 'Profile picture updated successfully');
        } catch (error) {
          console.error('Error uploading profile picture:', error);
          Alert.alert(
            'Error',
            error instanceof Error
              ? error.message
              : 'Failed to update profile picture. Please try again.'
          );
        } finally {
          setUploading(false);
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(
        'Error',
        error instanceof Error
          ? error.message
          : 'Failed to pick image. Please try again.'
      );
    }
  };

  return (
    <SafeAreaView style={{position: "relative"}} className='p-4 flex-1'>
      <View className='flex justify-between items-center flex-row'>
        <View>
          <Text className='font-HelveticaNeueBold text-[24px]'>Hello, {profile?.username || 'User'}</Text>
          <Text className='text-[#949494] font-HelveticaNeueMedium'>Welcome to Quizzy</Text>
        </View>
        <View className='flex-row items-center gap-2'>
          <TouchableOpacity
            onPress={handleProfilePicturePress}
            disabled={uploading}
          >
            <Image
              source={
                profile?.avatar_url
                  ? { uri: profile.avatar_url }
                  : require('../../../assets/images/Profile.png')
              }
              className="w-14 h-14 rounded-full"
              resizeMode="cover"
              defaultSource={require('../../../assets/images/Profile.png')}
            />
          </TouchableOpacity>
        </View>
      </View>

      <KeyboardAvoidingView
        className='mt-4'
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View className='flex flex-row items-center gap-2 px-6 py-3 bg-white rounded-full'>
                <Image source={require("../../../assets/images/Search.png")} className='w-7 h-7' />
                <TextInput
                placeholder="Search"
                className='flex-1 text-[##1d1d1d] font-HelveticaNeueMedium'
                />
                <View className='rounded-full p-3 bg-[#1d1d1d]'>
                  <Image source={require("../../../assets/images/Filter.png")} className='w-7 h-7 ' tintColor={changeIconColor ? "white" : "black"} />
                </View>
            </View>
        </TouchableWithoutFeedback>

        {/* <TouchableOpacity onPress={() => console.log(profile)}>
          <Text>Show</Text>
        </TouchableOpacity> */}

      </KeyboardAvoidingView>

      {/* <TouchableOpacity
            onPress={handleLogout}
            className='bg-[#1d1d1d] px-4 py-2 rounded-full'
          >
        <Text className='text-white font-HelveticaNeueMedium'>Logout</Text>
      </TouchableOpacity> */}
      <MiniLeaderboard />
      <Courses />
      <Play/>
    </SafeAreaView>
  )
}

export default home