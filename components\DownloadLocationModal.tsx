import { Ionicons } from '@expo/vector-icons';
import { useState } from 'react';
import { Alert, Modal, Text, TouchableOpacity, View } from 'react-native';
import { StorageLocation } from '../lib/downloadUtils';

interface DownloadLocationModalProps {
  visible: boolean;
  fileName: string;
  onClose: () => void;
  onDownload: (location: StorageLocation) => void;
}

const DownloadLocationModal = ({ 
  visible, 
  fileName, 
  onClose, 
  onDownload 
}: DownloadLocationModalProps) => {
  const [selectedLocation, setSelectedLocation] = useState<StorageLocation>(StorageLocation.APP_STORAGE);

  const handleDownload = () => {
    onDownload(selectedLocation);
    onClose();
  };

  const showLocationInfo = (location: StorageLocation) => {
    const info = location === StorageLocation.APP_STORAGE
      ? {
          title: 'App Storage',
          description: 'Files are saved in the app\'s private storage. They can only be accessed through this app and will be removed if you uninstall the app.',
          pros: ['No permissions required', 'Fast and reliable', 'Always works'],
          cons: ['Only accessible through this app', 'Removed when app is uninstalled']
        }
      : {
          title: 'Device Downloads',
          description: 'Files are saved to your device\'s Downloads folder where you can access them through your file manager and other apps.',
          pros: ['Accessible through file manager', 'Can open with other apps', 'Persists after app uninstall'],
          cons: ['Requires storage permission', 'May fail on some devices']
        };

    Alert.alert(
      info.title,
      `${info.description}\n\n✅ Pros:\n${info.pros.map(p => `• ${p}`).join('\n')}\n\n❌ Cons:\n${info.cons.map(c => `• ${c}`).join('\n')}`,
      [{ text: 'OK' }]
    );
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-black/50 justify-end">
        <View className="bg-white rounded-t-3xl p-6">
          {/* Header */}
          <View className="flex-row justify-between items-center mb-6">
            <Text className="text-xl font-bold text-gray-900">Choose Download Location</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          {/* File Info */}
          <View className="bg-gray-50 rounded-xl p-4 mb-6">
            <Text className="text-gray-600 text-sm mb-1">Downloading:</Text>
            <Text className="text-gray-900 font-medium" numberOfLines={2}>{fileName}</Text>
          </View>

          {/* Storage Options */}
          <View className="space-y-3 mb-6">
            {/* App Storage Option */}
            <TouchableOpacity
              onPress={() => setSelectedLocation(StorageLocation.APP_STORAGE)}
              className={`border-2 rounded-xl p-4 ${
                selectedLocation === StorageLocation.APP_STORAGE
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 bg-white'
              }`}
            >
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center flex-1">
                  <View className={`w-12 h-12 rounded-full justify-center items-center mr-4 ${
                    selectedLocation === StorageLocation.APP_STORAGE
                      ? 'bg-blue-100'
                      : 'bg-gray-100'
                  }`}>
                    <Ionicons 
                      name="phone-portrait" 
                      size={24} 
                      color={selectedLocation === StorageLocation.APP_STORAGE ? '#3B82F6' : '#6B7280'} 
                    />
                  </View>
                  <View className="flex-1">
                    <Text className={`font-semibold text-base ${
                      selectedLocation === StorageLocation.APP_STORAGE
                        ? 'text-blue-900'
                        : 'text-gray-900'
                    }`}>
                      App Storage
                    </Text>
                    <Text className="text-gray-500 text-sm">
                      Private app storage (Recommended)
                    </Text>
                  </View>
                </View>
                <View className="flex-row items-center">
                  <TouchableOpacity
                    onPress={() => showLocationInfo(StorageLocation.APP_STORAGE)}
                    className="p-2 mr-2"
                  >
                    <Ionicons name="information-circle-outline" size={20} color="#6B7280" />
                  </TouchableOpacity>
                  <View className={`w-6 h-6 rounded-full border-2 justify-center items-center ${
                    selectedLocation === StorageLocation.APP_STORAGE
                      ? 'border-blue-500 bg-blue-500'
                      : 'border-gray-300'
                  }`}>
                    {selectedLocation === StorageLocation.APP_STORAGE && (
                      <Ionicons name="checkmark" size={16} color="white" />
                    )}
                  </View>
                </View>
              </View>
            </TouchableOpacity>

            {/* Device Downloads Option */}
            <TouchableOpacity
              onPress={() => setSelectedLocation(StorageLocation.DEVICE_DOWNLOADS)}
              className={`border-2 rounded-xl p-4 ${
                selectedLocation === StorageLocation.DEVICE_DOWNLOADS
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 bg-white'
              }`}
            >
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center flex-1">
                  <View className={`w-12 h-12 rounded-full justify-center items-center mr-4 ${
                    selectedLocation === StorageLocation.DEVICE_DOWNLOADS
                      ? 'bg-green-100'
                      : 'bg-gray-100'
                  }`}>
                    <Ionicons 
                      name="folder" 
                      size={24} 
                      color={selectedLocation === StorageLocation.DEVICE_DOWNLOADS ? '#10B981' : '#6B7280'} 
                    />
                  </View>
                  <View className="flex-1">
                    <Text className={`font-semibold text-base ${
                      selectedLocation === StorageLocation.DEVICE_DOWNLOADS
                        ? 'text-green-900'
                        : 'text-gray-900'
                    }`}>
                      Device Downloads
                    </Text>
                    <Text className="text-gray-500 text-sm">
                      Downloads folder (Requires permission)
                    </Text>
                  </View>
                </View>
                <View className="flex-row items-center">
                  <TouchableOpacity
                    onPress={() => showLocationInfo(StorageLocation.DEVICE_DOWNLOADS)}
                    className="p-2 mr-2"
                  >
                    <Ionicons name="information-circle-outline" size={20} color="#6B7280" />
                  </TouchableOpacity>
                  <View className={`w-6 h-6 rounded-full border-2 justify-center items-center ${
                    selectedLocation === StorageLocation.DEVICE_DOWNLOADS
                      ? 'border-green-500 bg-green-500'
                      : 'border-gray-300'
                  }`}>
                    {selectedLocation === StorageLocation.DEVICE_DOWNLOADS && (
                      <Ionicons name="checkmark" size={16} color="white" />
                    )}
                  </View>
                </View>
              </View>
            </TouchableOpacity>
          </View>

          {/* Action Buttons */}
          <View className="flex-row space-x-3">
            <TouchableOpacity
              onPress={onClose}
              className="flex-1 bg-gray-200 rounded-xl p-4"
            >
              <Text className="text-gray-700 font-semibold text-center">Cancel</Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleDownload}
              className={`flex-1 rounded-xl p-4 ${
                selectedLocation === StorageLocation.APP_STORAGE
                  ? 'bg-blue-500'
                  : 'bg-green-500'
              }`}
            >
              <Text className="text-white font-semibold text-center">Download</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default DownloadLocationModal;
